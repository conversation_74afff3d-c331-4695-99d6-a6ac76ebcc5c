以下是基于您需求设计的后台服务接口权限系统文档，包含结构说明、权限逻辑和可视化图表：

---

# 后台服务权限系统设计文档

## 1. 接口路由规则
```markdown
├── /admin/api
│   ├── /public/{controller}/xxx       # 开放接口（无需鉴权）
│   ├── /system/{controller}/xxx       # 系统级接口（仅校验Token+控制器权限）
│   └── /{project_id}/{controller}/xxx # 项目级接口（校验Token+项目权限+控制器权限）
```

```
┌──────────┐        ┌──────────┐
│   User   │        │   Role   │
└───┬──┬───┘        └────┬─────┘
    │  │                │
    │  └─User-Project───┘
    │  └─User-Controller
    └─Role-User (多对多)
       Role-Project
       Role-Controller
```

## 2. 权限模型设计
### 2.1 核心实体关系
```mermaid
erDiagram
    USER ||--o{ USER_ROLE : has
    USER ||--o{ USER_PROJECT : binds
    USER ||--o{ USER_CONTROLLER : binds
    ROLE ||--o{ ROLE_PROJECT : binds
    ROLE ||--o{ ROLE_CONTROLLER : binds
```

### 2.2 权限校验流程图
```mermaid
graph TD
    A[请求到达] --> B{路由类型?}
    B -->|Public| C[直接放行]
    B -->|System| D[校验Token有效性]
    B -->|Project| E[校验Token+项目权限]
    E --> F{是否通过?}
    F -->|否| G[返回403]
    F -->|是| H[校验Controller权限]
    H --> I{是否通过?}
    I -->|否| G
    I -->|是| J[执行业务逻辑]
```

## 3. 权限校验逻辑
### 3.1 校验优先级（从高到低）
1. User-Controller绑定（用户单独配置的控制器权限）
2. User-Project绑定（用户单独配置的项目权限）
3. Role-Controller绑定（角色配置的控制器权限）
4. Role-Project绑定（角色配置的项目权限）

### 3.2 权限判定伪代码
```python
def check_permission(user, project_id, controller):
    # 用户级权限检查
    if user.has_controller_permission(controller):
        return True
    if user.has_project_permission(project_id):
        return True
        
    # 角色级权限检查
    for role in user.roles:
        if role.has_controller_permission(controller):
            return True
        if role.has_project_permission(project_id):
            return True
            
    return False
```

## 4. 数据库表设计
| 表名              | 关键字段                          |
|-------------------|----------------------------------|
| users             | id, username, ...                |
| roles             | id, name, ...                    |
| user_roles        | user_id, role_id                 |
| user_projects     | user_id, project_id              |
| user_controllers  | user_id, controller_name         |
| role_projects     | role_id, project_id              |
| role_controllers  | role_id, controller_name         |

## 5. 异常处理规则
| HTTP状态码 | 场景                          |
|------------|------------------------------|
| 401        | Token无效/过期                |
| 403        | 权限校验失败                  |
| 404        | 项目ID不存在/接口路径错误      |

## 6. 思维导图
```mermaid
mindmap
  root((权限系统))
    路由规则
      Public
      System
      Project
    权限维度
      用户级
        项目权限
        控制器权限
      角色级
        项目权限
        控制器权限
    校验流程
      Token校验
      项目权限校验
      控制器权限校验
```

## 7. 建议的扩展功能
1. 权限缓存：使用Redis缓存用户权限集合
2. 权限继承：支持角色继承关系
3. 权限日志：记录权限变更和访问日志

---

该设计可通过以下工具实现可视化：
1. 使用Postman测试不同路由的权限表现
2. 用Swagger标注接口权限要求
3. 通过Admin后台管理权限绑定关系

需要更详细的实现代码或具体模块说明可以告诉我！
# 后台管程-基础程序设计

## 需求说明

本管理员后台系统的时序图和流程图设计，时序图的参与者是：
1. 管理员（页面发送账号密码）
2. 中间件（负责每个请求携带token的校验和路由权限）
3. 业务服务（负责具体的业务逻辑）
4. Redis（缓存token version和权限列表）
5. 中台db

涉及的业务包含：
1. 登录下发路由列表
2. 管理员有不同的role，role不同可以访问的路由也不同
3. 管理员可以管理账号，超级管理员可以操作role绑定路由访问权限，也可以直接把某个路由赋予某个具体管理员用户
5. 登录模块使用jwt，使用token version实现一键踢人
6. token version和管理员权限列表存储在Redis中，中间件直接从Redis获取信息
7. 修改管理员路由权限时，同时修改Redis和数据库，数据库操作带事务

## 流程图设计

### 1. 管理员登录流程图

```mermaid
flowchart TD
    A[管理员] --> B[输入账号密码]
    B --> C{验证账号密码}
    C -->|验证失败| D[返回错误信息]
    D --> B
    C -->|验证成功| E[生成JWT Token]
    E --> F[查询用户角色]
    F --> G[获取角色对应的路由列表]
    G --> H[将Token version存入Redis]
    H --> I[将用户权限列表存入Redis]
    I --> J[返回Token和路由列表]
    J --> K[管理员登录成功]
```

### 2. 权限管理流程图

```mermaid
flowchart TD
    A[请求访问路由] --> B{中间件验证Token}
    B -->|Token无效| C[返回401未授权]
    B -->|Token有效| D[从Redis获取Token version]
    D --> E{验证Token version}
    E -->|不匹配| F[返回401未授权]
    E -->|匹配| G[从Redis获取用户权限]
    G --> H{检查路由权限}
    H -->|无权限| I[返回403禁止访问]
    H -->|有权限| J[转发请求到业务服务]
    J --> K[业务服务处理请求]
    K --> L[返回处理结果]
```

### 3. 账号管理流程图

```mermaid
flowchart TD
    A[超级管理员] --> B{选择操作类型}
    B --> C[创建管理员账号]
    B --> D[修改管理员信息]
    B --> E[删除管理员账号]
    B --> F[分配角色权限]
    C --> G[填写账号信息]
    G --> H[开启数据库事务]
    H --> I[保存到数据库]
    I --> J[提交事务]
    D --> K[修改账号信息]
    K --> L[开启数据库事务]
    L --> M[更新数据库]
    M --> N[更新Redis中的权限信息]
    N --> O[提交事务]
    E --> P[开启数据库事务]
    P --> Q[从数据库删除账号]
    Q --> R[从Redis删除权限信息]
    R --> S[提交事务]
    F --> T[选择角色]
    F --> U[直接分配路由权限]
    T --> V[选择可访问路由]
    U --> V1[选择管理员用户]
    V1 --> W[选择特定路由]
    V --> X[开启数据库事务]
    X --> Y[保存角色-路由关系]
    Y --> Z[更新Redis中的权限信息]
    Z --> AA[提交事务]
    W --> AB[开启数据库事务]
    AB --> AC[保存用户-路由关系]
    AC --> AD[更新Redis中的权限信息]
    AD --> AE[提交事务]
```


### 4. 一键踢人/登出流程图

```mermaid
flowchart TD
    %% 超级管理员踢出用户分支
    A1[超级管理员] --> B1[选择用户]
    B1 --> C1[执行踢出操作]
    C1 --> D[准备更新token version]
    
    %% 管理员主动登出分支
    A2[管理员] --> B2[点击登出按钮]
    B2 --> D
    
    %% 共同流程
    D --> E[增加用户token version]
    E --> G[更新Redis中的token version]
    
    %% 不同结果分支
    G --> I1[用户下次请求时Token验证失败]
    I1 --> J1[用户被强制登出]
    
    G --> I2[清除本地Token]
    I2 --> J2[跳转到登录页面]
```

## 时序图设计

### 1. 登录及路由获取时序图

```mermaid
sequenceDiagram
    participant A as 管理员
    participant M as 中间件
    participant S as 业务服务
    participant R as Redis
    participant D as 中台DB
    
    A->>S: 发送账号密码
    S->>D: 查询账号信息
    D-->>S: 返回账号信息
    S->>S: 验证密码
    alt 验证失败
        S-->>A: 返回登录失败信息
    else 验证成功
        S->>D: 查询用户角色
        D-->>S: 返回角色信息
        S->>D: 查询角色对应路由权限
        D-->>S: 返回路由列表
        S->>S: 生成JWT Token
        S->>R: 存储Token version
        S->>R: 存储用户权限列表
        S-->>A: 返回Token和路由列表
    end
```

### 2. 权限验证时序图

```mermaid
sequenceDiagram
    participant A as 管理员
    participant M as 中间件
    participant S as 业务服务
    participant R as Redis
    participant D as 中台DB
    
    A->>M: 请求访问路由(携带Token)
    M->>M: 验证Token有效性
    alt Token无效
        M-->>A: 返回401未授权
    else Token有效
        M->>R: 获取Token version
        R-->>M: 返回Token version
        M->>M: 验证Token version
        alt Token version不匹配
            M-->>A: 返回401未授权
        else Token version匹配
            M->>R: 获取用户权限
            R-->>M: 返回用户权限信息
            M->>M: 检查路由访问权限
            alt 无权限
                M-->>A: 返回403禁止访问
            else 有权限
                M->>S: 转发请求
                S->>S: 处理业务逻辑
                S->>D: 数据库操作
                D-->>S: 返回操作结果
                S-->>A: 返回处理结果
            end
        end
    end
```

### 3. 角色管理时序图

```mermaid
sequenceDiagram
    participant A as 超级管理员
    participant M as 中间件
    participant S as 业务服务
    participant R as Redis
    participant D as 中台DB
    
    A->>M: 请求管理角色(携带Token)
    M->>R: 验证Token和权限
    R-->>M: 返回验证结果
    M->>S: 转发请求
    S->>D: 查询现有角色列表
    D-->>S: 返回角色列表
    S-->>A: 显示角色列表
    A->>M: 提交角色-路由权限配置
    M->>S: 转发请求
    S->>D: 开启数据库事务
    S->>D: 保存角色-路由关系
    D-->>S: 返回保存结果
    alt 保存成功
        S->>R: 更新Redis中的权限信息
        S->>D: 提交事务
        S-->>A: 返回配置成功信息
    else 保存失败
        S->>D: 回滚事务
        S-->>A: 返回配置失败信息
    end
```

### 4. 账号管理时序图

```mermaid
sequenceDiagram
    participant A as 管理员
    participant M as 中间件
    participant S as 业务服务
    participant R as Redis
    participant D as 中台DB
    
    A->>M: 请求管理账号(携带Token)
    M->>R: 验证Token和权限
    R-->>M: 返回验证结果
    M->>S: 转发请求
    S->>D: 查询账号列表
    D-->>S: 返回账号列表
    S-->>A: 显示账号列表
    
    alt 创建账号
        A->>M: 4.1提交新账号信息
        M->>S: 转发请求
        S->>D: 开启数据库事务
        S->>D: 保存账号信息
        D-->>S: 返回保存结果
        alt 保存成功
            S->>D: 提交事务
            S-->>A: 返回创建成功信息
        else 保存失败
            S->>D: 回滚事务
            S-->>A: 返回创建失败信息
        end
    else 修改账号
        A->>M: 4.2提交修改信息
        M->>S: 转发请求
        S->>D: 开启数据库事务
        S->>D: 更新账号信息
        D-->>S: 返回更新结果
        alt 更新成功
            S->>R: 更新Redis中的权限信息(如果涉及权限变更)
            S->>D: 提交事务
            S-->>A: 返回修改成功信息
        else 更新失败
            S->>D: 回滚事务
            S-->>A: 返回修改失败信息
        end
    else 删除账号
        A->>M: 4.3提交删除请求
        M->>S: 转发请求
        S->>D: 开启数据库事务
        S->>D: 删除账号信息
        D-->>S: 返回删除结果
        alt 删除成功
            S->>R: 从Redis删除相关权限信息
            S->>D: 提交事务
            S-->>A: 返回删除成功信息
        else 删除失败
            S->>D: 回滚事务
            S-->>A: 返回删除失败信息
        end
    end
```



### 5. 一键踢人/登出时序图

```mermaid
sequenceDiagram
    participant A as 超级管理员
    participant MA as 管理员
    participant M as 中间件
    participant S as 业务服务
    participant R as Redis
    participant U as 被踢用户
    
    alt 超级管理员踢出用户
        A->>M: 5.1请求踢出用户(携带Token)
        M->>R: 验证Token和权限
        R-->>M: 返回验证结果
        M->>S: 转发请求
        S->>S: 准备更新token version
        S->>R: 更新Redis中的token version
        R-->>S: 返回更新结果
        alt 更新成功
            S-->>A: 返回操作成功信息
        else 更新失败
            S-->>A: 返回操作失败信息
        end
        
        U->>M: 发送请求(携带旧Token)
        M->>R: 获取Token version
        R-->>M: 返回token version
        M->>M: 验证Token version
        M-->>U: 返回401未授权(token version不匹配)
    else 管理员主动登出
        MA->>M: 5.2点击登出按钮(携带Token)
        M->>R: 验证Token和权限
        R-->>M: 返回验证结果
        M->>S: 转发请求
        S->>S: 准备更新token version
        S->>R: 更新Redis中的token version
        R-->>S: 返回更新结果
        alt 更新成功
            S-->>MA: 返回操作成功信息
            MA->>MA: 清除本地Token
            MA->>MA: 跳转到登录页面
        else 更新失败
            S-->>MA: 返回操作失败信息
        end
    end
```


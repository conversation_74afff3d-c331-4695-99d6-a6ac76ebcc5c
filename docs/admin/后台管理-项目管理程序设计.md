# 后台管理-项目管理程序设计
## 需求
本文档为后台管理-项目管理的模块相关设计需求，要求提供

项目project管理功能：
* 项目列表展示
* 项目的新建|编辑

项目包含应用的管理功能,一个项目有多个应用：
* 项目拥有的应用列表展示
* 项目拥有的应用的新建|编辑，新建应用会获得应用的app_Key和app_secret
* 项目拥有的应用的删除



## 流程图

### 1. 项目管理流程图

```mermaid
flowchart TD
    A[管理员] --> B[查看项目列表]
    B --> C{选择操作}
    C --> D[新建项目]
    C --> E[编辑项目]
    C --> F[查看项目应用]
    D --> G[填写项目信息]
    G --> H[开启数据库事务]
    H --> I[保存到数据库]
    I --> J[提交事务]
    E --> K[修改项目信息]
    K --> L[开启数据库事务]
    L --> M[更新数据库]
    M --> N[提交事务]
    F --> O[显示项目拥有的应用列表]
    O --> P{选择操作}
    P --> Q[返回项目列表]
    P --> R[管理应用]
    R --> S[进入应用管理流程]
```

### 2. 应用管理流程图

```mermaid
flowchart TD
    A[管理员] --> B[查看项目拥有的应用列表]
    B --> C{选择操作}
    C --> D[添加新应用]
    C --> E[修改应用信息]
    C --> F[删除应用]
    C --> Z[返回项目列表]
    D --> G[填写应用信息]
    G --> G1[生成app_Key和app_secret]
    G1 --> H[开启数据库事务]
    H --> I[保存到数据库]
    I --> J[提交事务]
    E --> K[修改应用配置]
    K --> L[开启数据库事务]
    L --> M[更新数据库]
    M --> N[提交事务]
    F --> O[开启数据库事务]
    O --> P[从数据库删除应用]
    P --> Q[提交事务]
```

## 时序图

### 1. 项目管理时序图

```mermaid
sequenceDiagram
    participant A as 管理员
    participant M as 中间件
    participant S as 业务服务
    participant R as Redis
    participant D as 中台DB
    
    A->>M: 请求项目列表(携带Token)
    M->>R: 验证Token和权限
    R-->>M: 返回验证结果
    M->>S: 转发请求
    S->>D: 查询项目列表
    D-->>S: 返回项目列表
    S-->>A: 显示项目列表
    
    alt 新建/编辑项目
        A->>M: 提交项目信息
        M->>S: 转发请求
        S->>D: 开启数据库事务
        S->>D: 保存项目信息
        D-->>S: 返回保存结果
        alt 保存成功
            S->>D: 提交事务
            S-->>A: 返回操作成功信息
        else 保存失败
            S->>D: 回滚事务
            S-->>A: 返回操作失败信息
        end
    else 查看项目应用
        A->>M: 请求项目应用列表
        M->>S: 转发请求
        S->>D: 查询项目关联的应用列表
        D-->>S: 返回应用列表
        S-->>A: 显示项目拥有的应用列表
    end
```

### 2. 应用管理时序图


```mermaid
sequenceDiagram
    participant A as 管理员
    participant M as 中间件
    participant S as 业务服务
    participant R as Redis
    participant D as 中台DB
    
    A->>M: 请求项目拥有的应用列表(携带Token)
    M->>R: 验证Token和权限
    R-->>M: 返回验证结果
    M->>S: 转发请求
    S->>D: 查询项目关联的应用列表
    D-->>S: 返回应用列表
    S-->>A: 显示应用列表
    
    alt 新建应用
        A->>M: 提交新应用信息
        M->>S: 转发请求
        S->>S: 生成app_Key和app_secret
        S->>D: 开启数据库事务
        S->>D: 保存应用信息及与项目的关联
        D-->>S: 返回保存结果
        alt 保存成功
            S->>D: 提交事务
            S-->>A: 返回创建成功信息(包含app_Key和app_secret)
        else 保存失败
            S->>D: 回滚事务
            S-->>A: 返回创建失败信息
        end
    else 修改应用
        A->>M: 提交修改的应用信息
        M->>S: 转发请求
        S->>D: 开启数据库事务
        S->>D: 更新应用信息
        D-->>S: 返回更新结果
        alt 更新成功
            S->>D: 提交事务
            S-->>A: 返回修改成功信息
        else 更新失败
            S->>D: 回滚事务
            S-->>A: 返回修改失败信息
        end
    else 删除应用
        A->>M: 提交删除应用请求
        M->>S: 转发请求
        S->>D: 开启数据库事务
        S->>D: 删除应用及与项目的关联
        D-->>S: 返回删除结果
        alt 删除成功
            S->>D: 提交事务
            S-->>A: 返回删除成功信息
        else 删除失败
            S->>D: 回滚事务
            S-->>A: 返回删除失败信息
        end
    end
```
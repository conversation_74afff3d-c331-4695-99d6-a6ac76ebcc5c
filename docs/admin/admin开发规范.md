# mega-cloud-admin 开发规范

## 路由规则
admin的路由分为三个类型
* /admin/api/public/{controller}/xxx 表示无需鉴权，比如登陆接口  
* /admin/api/system/{controller}/xxx 表示系统级别的权限，比如创建项目，管理权限等，只校验token有效性
* /admin/api/{project_id}/{controller}/xxx  表示项目级别权限，除了校验token有效性，还需要校验项目权限，


权限分为2种
* project 项目级别功能权限
* system 系统级别功能权限


## Controller 

* 全部使用POST请求`@PostMapping`
* 类别后根据Controller，如果Controller命名为多个单词如`AppAuthController`，则为`/{projectId}/app/auth/xxx`


public举例:
```java
 /**
     * 管理员登录
     *
     * @param reqVO 登录请求参数
     * @return 登录响应结果
     */
    @PostMapping("/public/auth/login")
    @ApiOperation("管理员登录")
```

system举例

```java

    @PostMapping("/system/auth/profile")
    @ApiOperation("获取登录用户详细信息")
```

project举例

```java 
    /**
     * 分页查询应用列表
     */
    @PostMapping("/{projectId}/app/list")
```

## Dao

* project相关操作，路由一定会携带projectId，sql操作一定要携带
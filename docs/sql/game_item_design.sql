-- --------------------------------------------------------
-- Table structure for 1 游戏内-道具表配置
-- --------------------------------------------------------
CREATE TABLE `item` (
  `id` INT PRIMARY KEY NOT NULL AUTO_INCREMENT COMMENT '道具唯一ID',
  `name` VARCHAR(64) NOT NULL COMMENT '道具名称',
  `item_cate_id` INT  NOT NULL COMMENT '道具类型ID',
  `delsign` TINYINT NOT NULL DEFAULT 0 COMMENT '删除标志位: 0=正常, 1=已删除',
  `create_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='基础道具表';

-- 道具类型表
CREATE TABLE `item_cate` (
  `id` INT  NOT NULL AUTO_INCREMENT COMMENT '道具类型ID',
  `name` VARCHAR(20) NOT NULL COMMENT '道具类型名称',
  `code` VARCHAR(20) NOT NULL COMMENT '道具类型编码 比如Consumable, Material, Currency, Key, Chest',
  `remark` TEXT COMMENT '道具类型描述 道具类型: Consumable-消耗品 Material-材料 Currency-货币 Key-钥匙 Chest-宝箱',
  PRIMARY KEY (`item_type_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='道具类型表';

-- item_info表 配置道具的通用基础属性 是对item表的扩展
CREATE TABLE `item_info` (
  `item_id` INT NOT NULL COMMENT '关联item表的id',
  `rarity` TINYINT  NOT NULL DEFAULT 1 COMMENT '道具品质，1-5星',
  `level` INT  NOT NULL DEFAULT 1 COMMENT '道具等级，用于合成逻辑',
  `story` TEXT COMMENT '道具故事描述',
  `icon` VARCHAR(255) COMMENT '道具图标',
  `max_stack` INT  NOT NULL DEFAULT 99 COMMENT '最大堆叠数量',
  `weight` TINYINT  DEFAULT 1 COMMENT '重量值（影响能否放入某些格子）',
  `width` TINYINT  NOT NULL DEFAULT 1 COMMENT '物品在仓库中占用的横向格子数',
  `height` TINYINT  NOT NULL DEFAULT 1 COMMENT '物品在仓库中占用的纵向格子数'
  PRIMARY KEY (`item_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='道具通用基础属性配置表';


-- 消耗品道具配置表
CREATE TABLE `item_consumable` (
  `item_id` INT  NOT NULL COMMENT '关联item表的id',
  `consumable_name` VARCHAR(100) NOT NULL COMMENT '消耗品名称',
  `effect_type` TINYINT  NOT NULL COMMENT '效果类型',
  `effect_value` INT NOT NULL COMMENT '效果数值',
  `duration` INT  DEFAULT NULL COMMENT 'buff持续时间(秒)',
  `cooldown` INT  DEFAULT NULL COMMENT '使用冷却时间(秒)',
  `use_animation` VARCHAR(255) COMMENT '使用动画路径',
  `description` TEXT COMMENT '消耗品效果描述',
  `use_requirements` JSON COMMENT '使用条件(等级/职业等)',
  `use_target` TINYINT  DEFAULT 1 COMMENT '使用目标 1:自身 2:单体目标 3:范围目标',
  `use_range` INT  DEFAULT 0 COMMENT '使用范围(格数)',
  `use_sound` VARCHAR(255) COMMENT '使用音效路径',
  PRIMARY KEY (`item_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='消耗品道具详细配置';

-- 材料道具配置表
CREATE TABLE `item_material` (
  `item_id` INT  NOT NULL COMMENT '关联item表的id',
  `material_name` VARCHAR(100) NOT NULL COMMENT '材料名称',
  `material_type` TINYINT  NOT NULL COMMENT '材料类型',
  `expire_time` TIMESTAMP DEFAULT NULL COMMENT '过期时间',
  `source_description` TEXT COMMENT '获取途径描述',
  `description` TEXT COMMENT '消耗品效果描述',
  PRIMARY KEY (`item_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='材料道具详细配置';

-- 货币道具配置表
CREATE TABLE `item_currencry` (
  `item_id` INT  NOT NULL COMMENT '关联item表的id',
  `currency_name` VARCHAR(100) NOT NULL COMMENT '货币名称',
  `daily_obtain_limit` INT  DEFAULT NULL COMMENT '每日获取上限',
  `weekly_obtain_limit` INT  DEFAULT NULL COMMENT '每周获取上限',
  `is_premium` TINYINT  NOT NULL DEFAULT 0 COMMENT '是否为高级货币，高级货币金钱兑换',
  `description` VARCHAR(255) COMMENT '备注说明',
  PRIMARY KEY (`item_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='货币道具详细配置';


-- 钥匙道具配置表
CREATE TABLE `item_key` (
  `item_id` INT  NOT NULL COMMENT '关联item表的id',
  `key_name` VARCHAR(100) NOT NULL COMMENT '钥匙名称',
  `key_type` TINYINT  NOT NULL COMMENT '钥匙类型',
  `target_id` INT  NOT NULL COMMENT '对应目标ID(关卡/宝箱/建筑)',
  `use_count` INT  NOT NULL DEFAULT 1 COMMENT '可使用次数',
  `expire_time` TIMESTAMP DEFAULT NULL COMMENT '过期时间',
  `description` TEXT COMMENT '消耗品效果描述',
  PRIMARY KEY (`item_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='钥匙道具详细配置';

-- 宝箱道具配置表
CREATE TABLE `item_chest` (
  `item_id` INT  NOT NULL COMMENT '关联item表的id',
  `chest_name` VARCHAR(100) NOT NULL COMMENT '宝箱名称',
  `chest_type` TINYINT  NOT NULL COMMENT '宝箱类型',
  `required_key_id` INT  DEFAULT NULL COMMENT '需要的钥匙ID',
  `reward_pool_id` INT  NOT NULL COMMENT '奖励池ID',
  `open_animation` VARCHAR(255) COMMENT '开启动画路径',
  `preview_rewards` TINYINT  NOT NULL DEFAULT 0 COMMENT '是否可预览奖励',
  `description` TEXT COMMENT '消耗品效果描述',
  PRIMARY KEY (`item_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='宝箱道具详细配置';

-- 奖励池主表
CREATE TABLE `reward_pool` (
  `id` INT PRIMARY KEY NOT NULL AUTO_INCREMENT COMMENT '奖励池ID',
  `pool_name` VARCHAR(64) NOT NULL COMMENT '奖励池名称',
  `description` TEXT COMMENT '奖励池描述',
  `pool_type` TINYINT  NOT NULL COMMENT '奖励池类型 1:固定奖励 2:随机奖励 3:权重随机',
  `min_rewards` TINYINT  NOT NULL DEFAULT 1 COMMENT '最少抽取奖励数',
  `max_rewards` TINYINT  NOT NULL DEFAULT 1 COMMENT '最多抽取奖励数'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='奖励池配置表';

-- 奖励池详细配置表
CREATE TABLE `reward_pool_item` (
  `reward_pool_id` INT  NOT NULL COMMENT '关联reward_pools表的id',
  `item_id` INT  NOT NULL COMMENT '奖励道具ID',
  `min_count` INT  NOT NULL DEFAULT 1 COMMENT '最小数量',
  `max_count` INT  NOT NULL DEFAULT 1 COMMENT '最大数量',
  `weight` INT  NOT NULL DEFAULT 100 COMMENT '权重(用于随机抽取)',
  `pity_count` INT  NOT NULL DEFAULT 0 COMMENT '保值抽取次数（超出此次数才能出）',
  `guarantee_count` INT  NOT NULL DEFAULT 0 COMMENT '保底抽取次数（达到此次数必定出）',
  `position` INT  DEFAULT NULL COMMENT '固定奖励时的位置',
  PRIMARY KEY (`pool_id`, `item_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='奖励池道具配置表';

-- --------------------------------------------------------
-- Table structure for 2 游戏内-玩法规则
-- --------------------------------------------------------

CREATE TABLE `rule_item_merge` (
  `input_item_id` INT  NOT NULL COMMENT '用于合成的道具ID，两个相同的道具合成',
  `rule_name` VARCHAR(100) NOT NULL COMMENT '规则名称',
  `result_item_id` INT  NOT NULL COMMENT '合成结果的道具ID',
  `required_count` INT  NOT NULL DEFAULT 2 COMMENT '需要合成的相同道具数量，默认为2',
  `merge_score` INT DEFAULT 0 COMMENT '该次合成可获得的分数，可选',
  PRIMARY KEY (`input_item_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='道具合成规则表';

-- --------------------------------------------------------
-- Table structure for 3 游戏内-角色道具系统
-- --------------------------------------------------------
CREATE TABLE `character_currency` (
  `character_id` BIGINT  NOT NULL COMMENT '用户ID',
  `item_id` INT  NOT NULL COMMENT '道具ID',
  `currency_name` VARCHAR(100) NOT NULL COMMENT '货币名称',
  `current_amount` BIGINT  NOT NULL DEFAULT 0 COMMENT '当前数量',
  `create_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`character_id`, `item_id`),
  KEY `idx_character_id` (`character_id`),
  KEY `idx_item_id` (`item_id`),
  KEY `idx_update_time` (`update_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='角色货币表';

-- 角色消耗品表
CREATE TABLE `character_consumable` (
  `character_id` BIGINT  NOT NULL COMMENT '用户ID',
  `item_id` INT  NOT NULL COMMENT '道具ID',
  `current_amount` BIGINT  NOT NULL DEFAULT 0 COMMENT '当前数量',
  `create_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`character_id`, `item_id`),
  KEY `idx_character_id` (`character_id`),
  KEY `idx_item_id` (`item_id`),
  KEY `idx_update_time` (`update_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='角色消耗品表';

-- 角色材料表
CREATE TABLE `character_material` (
  `character_id` BIGINT  NOT NULL COMMENT '用户ID',
  `item_id` INT  NOT NULL COMMENT '道具ID',
  `current_amount` BIGINT  NOT NULL DEFAULT 0 COMMENT '当前数量',
  `create_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`character_id`, `item_id`),
  KEY `idx_character_id` (`character_id`),
  KEY `idx_item_id` (`item_id`),
  KEY `idx_update_time` (`update_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='角色材料表';



-- --------------------------------------------------------
-- Table structure for 4 游戏业务  角色道具流水
-- --------------------------------------------------------
-- 业务字典表
CREATE TABLE `business_dict` (
  `id` BIGINT  PRIMARY KEY AUTO_INCREMENT COMMENT '记录ID',
  `name` VARCHAR(100) NOT NULL COMMENT '业务名称 比如活动xx,',
  `create_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='业务字典表';

-- 角色货币流水表
CREATE TABLE `character_currency_turnover` (
  `id` BIGINT  PRIMARY KEY AUTO_INCREMENT COMMENT '记录ID',
  `character_id` BIGINT  NOT NULL COMMENT '用户ID',
  `item_id` INT  NOT NULL COMMENT '货币类道具ID',
  `change_amount` BIGINT NOT NULL COMMENT '变更数量(正数为增加,负数为减少)',
  `transaction_id` VARCHAR(128) NOT NULL COMMENT '事务id,一般为生成的uuid',
  `business_dict_id` VARCHAR(128) NOT NULL COMMENT '业务字典id',
  `create_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_date` DATE GENERATED ALWAYS AS (DATE(create_time)) STORED COMMENT '创建日期',
  KEY `idx_character_id` (`character_id`),
  KEY `idx_item_id` (`item_id`),
  KEY `idx_create_time` (`create_time`),
  KEY `idx_create_date` (`create_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='角色货币流水表';


-- 角色消耗品流水表
CREATE TABLE `character_consumable_turnover` (
  `id` BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '记录ID',
  `character_id` BIGINT NOT NULL COMMENT '用户ID',
  `item_id` INT NOT NULL COMMENT '消耗品道具ID',
  `change_amount` BIGINT NOT NULL COMMENT '变更数量(正数为增加,负数为减少)',
  `transaction_id` VARCHAR(128) NOT NULL COMMENT '事务id,一般为生成的uuid',
  `business_dict_id` VARCHAR(128) NOT NULL COMMENT '业务字典id',
  `create_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_date` DATE GENERATED ALWAYS AS (DATE(create_time)) STORED COMMENT '创建日期',
  KEY `idx_character_id` (`character_id`),
  KEY `idx_item_id` (`item_id`),
  KEY `idx_create_time` (`create_time`),
  KEY `idx_create_date` (`create_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='角色消耗品流水表';



-- 角色材料流水表
CREATE TABLE `character_material_turnover` (
  `id` BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '记录ID',
  `character_id` BIGINT NOT NULL COMMENT '用户ID',
  `item_id` INT NOT NULL COMMENT '材料道具ID',
  `change_amount` BIGINT NOT NULL COMMENT '变更数量(正数为增加,负数为减少)',
  `transaction_id` VARCHAR(128) NOT NULL COMMENT '事务id,一般为生成的uuid',
  `business_dict_id` VARCHAR(128) NOT NULL COMMENT '业务字典id',
  `create_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_date` DATE GENERATED ALWAYS AS (DATE(create_time)) STORED COMMENT '创建日期',
  KEY `idx_character_id` (`character_id`),
  KEY `idx_item_id` (`item_id`),
  KEY `idx_create_time` (`create_time`),
  KEY `idx_create_date` (`create_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='角色材料流水表';



-- --------------------------------------------------------
-- Table structure for 5 游戏业务  角色仓库
-- --------------------------------------------------------

--仓库类型表
CREATE TABLE `inventory_type` (
  `id` INT  PRIMARY KEY AUTO_INCREMENT COMMENT '记录ID',
  `name` VARCHAR(100) NOT NULL COMMENT '仓库类型名称 仓库类型 Material:材料 Equipment:装备 Chest:宝箱 Quest:任务 Misc:杂项',
  `create_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='仓库类型表';


CREATE TABLE `character_inventory` (
  `character_id` BIGINT  NOT NULL COMMENT '玩家ID',
  `inventory_type_id` INT  NOT NULL COMMENT '仓库类型ID',
  `max_slots` INT  NOT NULL DEFAULT 100 COMMENT '最大格子数量',
  `create_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `max_weight_per_slot` INT  NOT NULL DEFAULT 100 COMMENT '每个格子可承载的最大重量',
  `create_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`character_id`, `inventory_type_id`),
  KEY `idx_character` (`character_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='角色仓库基础表';

CREATE TABLE `character_inventory_item` (
  `character_id` BIGINT  NOT NULL COMMENT '玩家ID',
  `inventory_type_id` INT  NOT NULL COMMENT '仓库类型ID',
  `item_id` BIGINT  NOT NULL DEFAULT 0 COMMENT '道具ID，如果为0则表示该格子为空',
  `slot_index` INT  NOT NULL COMMENT '格子编号（用于手动排序/位置交换）',
  `stack_count` INT  NOT NULL DEFAULT 1 COMMENT '该格子堆叠了多少个此道具',
  `max_stack_count` INT  NOT NULL DEFAULT 99 COMMENT '该格子能堆叠该道具的最大数量',
  `viewed` TINYINT(1) NOT NULL DEFAULT 0 COMMENT '0未查看 1已查看',
  `bound` TINYINT(1) NOT NULL DEFAULT 0 COMMENT '0未绑定 1已绑定',
  `create_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '获得时间',
  `update_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后操作时间',
  PRIMARY KEY (`character_id`, `inventory_type_id`, `slot_index`),
  KEY `idx_item` (`item_id`),
  KEY `idx_character_item` (`character_id`, `item_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='角色仓库道具表';

-- --------------------------------------------------------
-- Table structure for 3 游戏业务  棋盘
-- --------------------------------------------------------

CREATE TABLE `board_template` (
  `board_id` INT  NOT NULL AUTO_INCREMENT,
  `name` VARCHAR(64) NOT NULL,
  `rows` TINYINT  NOT NULL DEFAULT 10,
  `cols` TINYINT  NOT NULL DEFAULT 10,
  `initial_state` JSON NOT NULL COMMENT '初始棋盘格子配置：每个格子的item_id与锁状态',
  `applied_stage` VARCHAR(64) COMMENT '对应关卡或剧情ID',
  `max_steps` INT  NOT NULL DEFAULT 10 COMMENT '最大步数',
  PRIMARY KEY (`board_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='棋盘模板定义表';

CREATE TABLE `board_template_cell_config` (
  `board_id` INT  NOT NULL COMMENT '棋盘模板ID',
  `row_index` TINYINT  NOT NULL COMMENT '行号',
  `col_index` TINYINT  NOT NULL COMMENT '列号',
  `item_id` INT  NOT NULL DEFAULT 0 COMMENT '格子初始道具ID，0表示空',
  `item_level` TINYINT  NOT NULL DEFAULT 1 COMMENT '道具等级',
  `is_locked` BOOLEAN NOT NULL DEFAULT FALSE COMMENT '格子是否锁定',
  `special_effect` TINYINT  DEFAULT NULL COMMENT '特殊效果类型',
  `effect_params` JSON DEFAULT NULL COMMENT '特殊效果参数',
  PRIMARY KEY (`board_id`, `row_index`, `col_index`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='棋盘模板格子配置表';


CREATE TABLE `account_board_state` (
  `character_id` BIGINT  NOT NULL,
  `board_id` INT  NOT NULL DEFAULT 1 COMMENT '棋盘模版id',
  `board_state` JSON NOT NULL COMMENT '整个棋盘的快照状态',
  `step_count` INT  DEFAULT 0 COMMENT '步数记录',
  `update_time` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`character_id`, `board_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户棋盘状态表';

-- 以下为 棋盘的json参考 -- 
{
  "rows": 5,
  "cols": 5,
  "cells": [
    [{"item_id": 101, "level": 1}, {"item_id": 0}, ..., {"item_id": 102, "level": 2}],
    ...
    [{"item_id": 0}, ..., {"item_id": 105, "level": 4}]
  ],
  "last_step_time": **********,
  "step_count": 27,
  "combo_count": 3
}


CREATE TABLE `story_chapter` (
  `id` BIGINT PRIMARY KEY AUTO_INCREMENT,
  `title` VARCHAR(100) NOT NULL COMMENT '章节标题',
  `order_index` INT NOT NULL COMMENT '章节顺序',
  `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (`story_id`) REFERENCES `story`(`id`)
) COMMENT='剧情章节表';


CREATE TABLE `story_scene` (
  `id` BIGINT PRIMARY KEY AUTO_INCREMENT,
  `chapter_id` BIGINT NOT NULL COMMENT '所属章节ID',
  `scene_type` VARCHAR(20) NOT NULL COMMENT '片段类型',
  `content` TEXT COMMENT '剧情内容',
  `game_dialog_id` BIGINT DEFAULT NULL COMMENT '关联的游戏对话ID',
  `order_index` INT NOT NULL COMMENT '片段顺序',
  `next_scene_id` BIGINT DEFAULT NULL COMMENT '默认跳转的下一个片段ID',
  `bg_image` VARCHAR(255) DEFAULT NULL COMMENT '背景图资源路径',
  `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (`chapter_id`) REFERENCES `story_chapter`(`id`)
) COMMENT='剧情片段表';

CREATE TABLE `story_choice` (
  `id` BIGINT PRIMARY KEY AUTO_INCREMENT,
  `scene_id` BIGINT NOT NULL COMMENT '所属片段ID',
  `choice_text` VARCHAR(255) NOT NULL COMMENT '选项文本',
  `next_scene_id` BIGINT NOT NULL COMMENT '选择后跳转的片段ID',
  `order_index` INT NOT NULL COMMENT '选项顺序',
  `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (`scene_id`) REFERENCES `story_scene`(`id`)
) COMMENT='剧情选择表';

CREATE TABLE `player_story_progress` (
  `id` BIGINT PRIMARY KEY AUTO_INCREMENT,
  `player_id` BIGINT NOT NULL COMMENT '玩家ID',
  `chapter_id` BIGINT NOT NULL COMMENT '当前章节ID',
  `current_scene_id` BIGINT DEFAULT NULL COMMENT '当前进行到的片段ID',
  `status` VARCHAR(20) DEFAULT 'in_progress' COMMENT '状态：in_progress / finished',
  `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  UNIQUE KEY `uniq_player_story` (`player_id`, `story_id`)
) COMMENT='玩家剧情进度';

CREATE TABLE `player_scene_choices` (
  `id` BIGINT PRIMARY KEY AUTO_INCREMENT,
  `player_id` BIGINT NOT NULL COMMENT '玩家ID',
  `scene_id` BIGINT NOT NULL COMMENT '片段ID',
  `choice_id` BIGINT NOT NULL COMMENT '选择ID',
  `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP,
  UNIQUE KEY `uniq_player_scene_choice` (`player_id`, `scene_id`, `choice_id`)
) COMMENT='玩家片段选择记录';

CREATE TABLE game_dialog (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    dialog_id VARCHAR(20) NOT NULL COMMENT '对话编号',
    character_name VARCHAR(50) NOT NULL COMMENT '角色名',
    character_avatar VARCHAR(100) COMMENT '角色头像资源路径',
    content TEXT NOT NULL COMMENT '对话内容',
    sort_order INT DEFAULT 0 COMMENT '排序序号'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='游戏关卡对话表';

-- --------------------------------------------------------
-- Table structure for 6 平台运营后台 (Console)*
-- --------------------------------------------------------

CREATE TABLE `admin_user` (
  `id` BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '管理员ID',
  `username` VARCHAR(128) UNIQUE NOT NULL COMMENT '管理员用户名',
  `password` CHAR(60) NOT NULL COMMENT 'bcrypt加密后的密码哈希值',
  `last_login_time` TIMESTAMP NULL COMMENT '最后登录时间',
  `create_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `delsign` TINYINT NOT NULL DEFAULT 0 COMMENT '删除标识: 0=未删除, 1=已删除',
  KEY `idx_username` (`username`),
  KEY `idx_role` (`role`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='运营后台管理员账户表';

CREATE TABLE `admin_role` (
  `id` BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT '角色ID',
  `name` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '角色名称',
  `description` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '角色描述',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `delsign` tinyint(4) NOT NULL DEFAULT '0' COMMENT '删除标识: 0=未删除, 1=已删除',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='运营后台角色表';


CREATE TABLE `admin_user_operate_log` (
  `id` BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '日志ID',
  `admin_user_id` BIGINT NOT NULL COMMENT '管理员ID',
  `api_path` VARCHAR(255) COMMENT 'API路径',
  `action` VARCHAR(100) NOT NULL COMMENT '操作类型描述',
  `target_table` VARCHAR(100) NOT NULL COMMENT '目标表名',
  `target_id` VARCHAR(100) NOT NULL COMMENT '目标记录ID',
  `detail` TEXT COMMENT '详细信息',
  `ip` VARCHAR(100) COMMENT 'IP地址',
  `device` VARCHAR(100) COMMENT '设备信息',
  `create_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  KEY `idx_user` (`admin_user_id`),
  KEY `idx_api_path` (`api_path`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='运营后台操作日志表';


CREATE TABLE `admin_router` (
  `id` BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT '路由ID',
  `backend_path` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '后端路由路径，空表示没有',
  `frontend_path` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '前端路由路径，空表示没有',
  `frontend_name` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '前端路由名称，空表示没有',
  `description` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '路由描述',
  `parent_admin_router_id` BIGINT(20) DEFAULT '0' COMMENT '父路由ID，0表示根路由',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `delsign` tinyint(4) NOT NULL DEFAULT '0' COMMENT '删除标识: 0=未删除, 1=已删除',
  PRIMARY KEY (`id`),
  KEY `idx_parent` (`parent_admin_router_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='运营后台角色表';

-- 新增  admin_user_role_binding 用户角色绑定表
CREATE TABLE `admin_user_role_binding` (
  `id` BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '绑定ID',
  `admin_user_id` INT NOT NULL COMMENT '管理员ID',
  `admin_role_id` INT NOT NULL COMMENT '角色ID',
  `create_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `delsign` TINYINT NOT NULL DEFAULT 0 COMMENT '删除标识: 0=未删除, 1=已删除',
  UNIQUE KEY `uk_admin_user_role` (`admin_user_id`, `admin_role_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='运营后台用户角色绑定表';


-- 新增  admin_role_project_binding 角色项目绑定表
CREATE TABLE `admin_role_project_binding` (
  `id` BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '绑定ID',
  `admin_role_id` BIGINT(20) NOT NULL COMMENT '角色ID',
  `project_id` BIGINT(20) NOT NULL COMMENT '项目ID',
  `create_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `delsign` TINYINT NOT NULL DEFAULT 0 COMMENT '删除标识: 0=未删除, 1=已删除',
  UNIQUE KEY `uk_role_project` (`admin_role_id`, `project_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='运营后台角色项目绑定表';


-- 新增  admin_user_project_binding 用户项目绑定表
CREATE TABLE `admin_user_project_binding` (
  `id` BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '绑定ID',
  `admin_user_id` BIGINT(20) NOT NULL COMMENT '管理员ID',
  `project_id` BIGINT(20) NOT NULL COMMENT '项目ID',
  `create_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `delsign` TINYINT NOT NULL DEFAULT 0 COMMENT '删除标识: 0=未删除, 1=已删除',
  UNIQUE KEY `uk_admin_user_project` (`admin_user_id`, `project_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='运营后台用户项目绑定表';


CREATE TABLE `admin_role_router_binding` (
  `id` BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '绑定ID',
  `admin_role_id` BIGINT(20) NOT NULL COMMENT '角色ID',
  `admin_router_id` BIGINT(20) NOT NULL COMMENT '路由ID',
  `create_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `delsign` TINYINT NOT NULL DEFAULT 0 COMMENT '状态: 0=正常, 1=禁用',
  UNIQUE KEY `uk_role_router` (`admin_role_id`, `admin_router_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='运营后台角色路由权限绑定表';

--运营后台管理员路由权限绑定表
CREATE TABLE `admin_user_router_binding` (
  `id` BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '绑定ID',
  `admin_user_id` BIGINT(20) NOT NULL COMMENT '管理员ID',
  `admin_router_id` BIGINT(20) NOT NULL COMMENT '路由ID',
  `create_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `delsign` TINYINT NOT NULL DEFAULT 0 COMMENT '状态: 0=正常, 1=禁用',
  UNIQUE KEY `uk_admin_user_router` (`admin_user_id`, `admin_router_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='运营后台角色路由权限绑定表';

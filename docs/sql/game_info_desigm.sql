
CREATE TABLE `app_info` (
  `app_id` INT UNSIGNED PRIMARY KEY COMMENT '应用ID',
  `icon_url` VARCHAR(512) COMMENT '应用图标URL',
  `cover_url` VARCHAR(512) COMMENT '应用封面图URL', 
  `description` TEXT COMMENT '应用描述',
  `update_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `package_name` VARCHAR(255) COMMENT '应用包名',
  `website_url` VARCHAR(512) COMMENT '官网地址',
  `privacy_url` VARCHAR(512) COMMENT '隐私政策地址',
  `terms_url` VARCHAR(512) COMMENT '服务条款地址',
  `support_email` VARCHAR(255) COMMENT '技术支持邮箱',
  `min_version` VARCHAR(20) COMMENT '最低支持版本',
  `latest_version` VARCHAR(20) COMMENT '最新版本',
  `delsign` TINYINT NOT NULL DEFAULT 0 COMMENT '删除标识: 0=未删除, 1=已删除',
  CONSTRAINT `fk_app_id` FOREIGN KEY (`app_id`) REFERENCES `app` (`id`),
  KEY `idx_app_id` (`app_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='app详细信息表';

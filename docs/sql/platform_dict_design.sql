-- --------------------------------------------------------
-- Table structure for 0 平台内 字典表 (dict system)
-- --------------------------------------------------------

-- 设备品牌表
CREATE TABLE `device_brand` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `brand_name` VARCHAR(255) COMMENT '品牌名称 比如 苹果',
  `delsign` TINYINT NOT NULL DEFAULT 0,
  PRIMARY KEY (`id`),
  UNIQUE KEY `brand_name_idx` (`brand_name`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='设备品牌表';


-- 设备os表
CREATE TABLE `device_os` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `os_name` VARCHA<PERSON>(255) COMMENT '操作系统名称 比如 ios',
  `delsign` TINYINT NOT NULL DEFAULT 0,
  PRIMARY KEY (`id`),
  UNIQUE KEY `os_name_idx` (`os_name`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='设备操作系统表';


-- 设备推送渠道表
CREATE TABLE `device_push_channel` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `channel_name` VARCHAR(255) COMMENT '渠道名称 比如 苹果',
  `delsign` TINYINT NOT NULL DEFAULT 0,
  PRIMARY KEY (`id`),
  UNIQUE KEY `channel_name_idx` (`channel_name`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='设备推送渠道表';

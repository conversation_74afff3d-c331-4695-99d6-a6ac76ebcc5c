-- --------------------------------------------------------
-- Table structure for 2 社交与群组 (Social & Groups)
-- --------------------------------------------------------
CREATE TABLE `character_edge` (
  `source_character_id` BIGINT UNSIGNED COMMENT '源用户ID',
  `target_character_id` BIGINT UNSIGNED NOT NULL COMMENT '目标用户ID',
  `status` ENUM('Confirmed', 'Sent', 'Received', 'Blocked', 'Blacklisted') NOT NULL DEFAULT 'Confirmed' COMMENT '好友关系状态: Confirmed=已确认的好友关系, Sent=发送的好友请求, Received=接收的好友请求, Banned=被禁用户, Blacklisted=拉黑的好友',
  `position` BIGINT NOT NULL COMMENT '用于排序或标记，例如好友关系的亲密度。',
  `create_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '关系创建时间',
  `update_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '关系更新时间',
  `delsign` TINYINT NOT NULL DEFAULT 0 COMMENT '删除标记: 0=未删除, 1=已删除',
  PRIMARY KEY (`source_character_id`,  `status`, `position`),
  UNIQUE KEY `relation_direction_key` (`source_character_id`, `target_character_id`, `status`), -- 允许反向关系
  KEY `target_idx` (`target_character_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户关系表';

CREATE TABLE `group` (
  `id` BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT COMMENT '群组唯一标识',
  `creator_character_id` BIGINT UNSIGNED NOT NULL COMMENT '创建者用户ID',
  `name` VARCHAR(255) NOT NULL COMMENT '群组名称',
  `description` TEXT COMMENT '群组描述',
  `announcement_title`   VARCHAR(255) COMMENT '群公告标题',
  `announcement_content` TEXT COMMENT '群公告内容',
  `announcement_time` TIMESTAMP NULL DEFAULT NULL COMMENT '群公告发布时间',
  `icon` VARCHAR(512) COMMENT '群组图标URL',
  `lang` VARCHAR(10) NOT NULL DEFAULT 'zh-CN' COMMENT '群组使用的语言',
  `member_count` INT NOT NULL DEFAULT 0 COMMENT '当前成员数量',
  `max_members` INT NOT NULL DEFAULT 100 COMMENT '最大成员数限制',
  `level` INT NOT NULL DEFAULT 0 COMMENT '群组等级',
  `status` TINYINT NOT NULL DEFAULT 0 COMMENT '群组状态: 0=公开, 1=私有',
  `create_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `delsign` TINYINT NOT NULL DEFAULT 0 COMMENT '删除标记: 0=未删除, 1=已删除',
  PRIMARY KEY (`id`),
  KEY `idx_creator` (`creator_character_id`),
  KEY `idx_name` (`name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='群组信息表';

CREATE TABLE `group_announcement_history` (
  `id` BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT COMMENT '公告历史ID',
  `group_id` BIGINT UNSIGNED NOT NULL COMMENT '群组ID',
  `announcement_title`   VARCHAR(255) COMMENT '群公告标题',
  `announcement_content` TEXT COMMENT '群公告内容',
  `creator_character_id` BIGINT UNSIGNED NOT NULL COMMENT '创建者用户ID',
  `create_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间'
  PRIMARY KEY (`id`),
  KEY `idx_group` (`group_id`),
  KEY `idx_creator` (`creator_character_id`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='群组公告历史表';



CREATE TABLE `group_character_edge` (
  `source_group_id` BIGINT UNSIGNED NOT NULL COMMENT '群组ID',
  `target_character_id` BIGINT UNSIGNED NOT NULL COMMENT '用户ID',
  `status` ENUM('SuperAdmin', 'Admin', 'Member', 'Request', 'Banned') NOT NULL DEFAULT 'Member' COMMENT '成员状态: SuperAdmin=超级管理员, Admin=管理员, Member=普通成员, Request=加入请求, Banned=被禁用户',
  `position` BIGINT NOT NULL DEFAULT 0 COMMENT '用于排序的位置值',
  `create_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '关系创建时间',
  `update_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '关系更新时间',
  `delsign` TINYINT NOT NULL DEFAULT 0 COMMENT '删除标记: 0=未删除, 1=已删除',
  PRIMARY KEY (`source_group_id`, `target_character_id`),
  KEY `idx_target_character` (`target_character_id`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='群组成员关系表';
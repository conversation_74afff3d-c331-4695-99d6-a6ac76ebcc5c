-- Game Base Database Schema for MySQL 5.7

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- --------------------------------------------------------
-- Table structure for 支付平台配置表
-- --------------------------------------------------------
CREATE TABLE `payment_platform` (
  `id` INT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
  `platform_code` varchar(64) UNIQUE NOT NULL COMMENT '支付平台编码（唯一）',
  `platform_name` varchar(128) NOT NULL COMMENT '支付平台名称',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `delsign` tinyint DEFAULT 0 COMMENT '逻辑删除标志：0正常，1删除'
);

-- --------------------------------------------------------
-- Table structure for 应用与支付平台配置表
-- --------------------------------------------------------
CREATE TABLE `payment_platform_app_config` (
  `id` INT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
  `app_id` INT NOT NULL COMMENT '应用ID',
  `payment_platform_id` INT NOT NULL COMMENT '关联的支付平台ID',
  `config` json NOT NULL COMMENT '平台配置信息（JSON格式）',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `delsign` tinyint DEFAULT 0 COMMENT '逻辑删除标志'
);


-- --------------------------------------------------------
-- Table structure for 支付订单表
-- --------------------------------------------------------
CREATE TABLE `payment_order` (
  `id` bigint PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
  `product_id` bigint NOT NULL COMMENT '商品ID',
  `order_no` varchar(64) UNIQUE NOT NULL COMMENT '支付平台订单号，比如alipay',
  `out_order_no` varchar(64) COMMENT '外显应用订单号（中台算法随机生成）',
  `total_amount` decimal(10,2) NOT NULL COMMENT '订单总金额',
  `currency` varchar(8) DEFAULT 'CNY' COMMENT '币种',
  `status` enum('INIT','SUCCESS','FAIL','REFUND') DEFAULT 'INIT' COMMENT '订单状态  INIT:初始化，SUCCESS:成功，FAIL:失败，REFUND:已退款',
  `sand_box` smallint COMMENT '是否为沙盒订单（1=是，0=否）',
  `pay_time` datetime COMMENT '支付完成时间',
  `extra` json COMMENT '扩展信息（JSON）',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `delsign` tinyint DEFAULT 0 COMMENT '逻辑删除标志'
  ,
  KEY `idx_product_id` (`product_id`),
  KEY `idx_order_no` (`order_no`),
  KEY `idx_out_order_no` (`out_order_no`)
);


-- --------------------------------------------------------
-- Table structure for 设备信息记录表
-- --------------------------------------------------------
CREATE TABLE `payment_order_device_info` (
  `id` INT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
  `payment_order_id` bigint NOT NULL COMMENT '关联的支付订单ID',
  `device_uuid` varchar(128) NOT NULL COMMENT '设备uuid',
  `app_id` INT NOT NULL COMMENT '应用ID',
  `device_name` varchar(255) COMMENT '设备名称',
  `device_os_id` INT COMMENT '操作系统id',
  `device_os_version` varchar(64) COMMENT '操作系统版本',
  `device_model` varchar(128) COMMENT '设备具体型号',
  `bvrs` varchar(255) COMMENT '应用版本号',
  `ip_address` varchar(64) COMMENT 'IP地址',
  `network_type` varchar(32) COMMENT '网络类型',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `delsign` tinyint DEFAULT 0 COMMENT '逻辑删除标志'
  ,
  KEY `idx_payment_order_id` (`payment_order_id`)
);

-- --------------------------------------------------------
-- Table structure for 商品配置表
-- --------------------------------------------------------
CREATE TABLE `payment_product_config` (
  `id` bigint PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
  `payment_platform_id` INT NOT NULL COMMENT '关联的支付平台ID',
  `external_product_id` varchar(128) NOT NULL COMMENT '外部商品ID （比如苹果平台的sku）',
  `product_name` varchar(128) COMMENT '商品名称',
  `is_subscription` boolean NOT NULL DEFAULT FALSE COMMENT '是否为订阅产品',
  `subscription_period_day` integer COMMENT '订阅周期（天）',
  `amount` decimal(10,2) NOT NULL COMMENT '商品价格',
  `currency` varchar(8) DEFAULT 'CNY' COMMENT '币种',
  `remark` varchar(255) COMMENT '备注',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `delsign` tinyint DEFAULT 0 COMMENT '逻辑删除标志'
  ,
  KEY `idx_payment_platform_id` (`payment_platform_id`),
  KEY `idx_external_product_id` (`external_product_id`)
);

-- --------------------------------------------------------
-- Table structure for 订阅扣款日志表
-- --------------------------------------------------------
CREATE TABLE `payment_subscription_charge_log` (
  `id` bigint PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
  `expected_charge_time` datetime NOT NULL COMMENT '计划扣款时间',
  `actual_charge_time` datetime DEFAULT NULL COMMENT '实际扣款时间',
  `status` varchar(32) DEFAULT 'PENDING' COMMENT '扣款状态',
  `error_code` varchar(64) COMMENT '错误码',
  `error_message` varchar(255) COMMENT '错误信息',
  `order_id` bigint COMMENT '关联订单ID',
  `subscrition_id` bigint COMMENT '订阅ID',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `delsign` tinyint DEFAULT 0 COMMENT '逻辑删除标志'
);

-- --------------------------------------------------------
-- Table structure for 用户订阅信息表
-- --------------------------------------------------------
CREATE TABLE `payment_subscription` (
  `id` bigint PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
  `product_config_id` bigint NOT NULL COMMENT '商品配置ID',
  `out_account_id` varchar(64) NOT NULL COMMENT '外部用户ID',
  `payment_order_id` bigint COMMENT '初始订阅订单ID',
  `status` enum('ACTIVE','INACTIVE','EXPIRED','CANCELLED') DEFAULT 'ACTIVE' COMMENT '订阅状态：ACTIVE=活跃,INACTIVE=不活跃,EXPIRED=已过期,CANCELLED=已取消',
  `next_charge_time` time COMMENT '下次扣款时间',
  `start_time` datetime NOT NULL COMMENT '订阅开始时间',
  `end_time` datetime NOT NULL COMMENT '订阅结束时间',
  `is_auto_renew` boolean DEFAULT TRUE COMMENT '是否自动续订',
  `cancel_reason` varchar(128) COMMENT '取消原因',
  `last_update_time` datetime COMMENT '最后更新时间',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `delsign` tinyint DEFAULT 0 COMMENT '逻辑删除标志'
  ,
  KEY `idx_product_config_id` (`product_config_id`),
  KEY `idx_out_account_id` (`out_account_id`),
  KEY `idx_payment_order_id` (`payment_order_id`)
);

-- --------------------------------------------------------
-- Table structure for 支付平台回调日志表
-- --------------------------------------------------------
CREATE TABLE `payment_callback_log` (
  `id` BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
  `platform_code` VARCHAR(64) NOT NULL COMMENT '支付平台编码，如 wechat, alipay, apple 等',
  `app_id` BIGINT NOT NULL COMMENT '应用ID',
  `order_no` varchar(64) UNIQUE NOT NULL COMMENT '支付平台订单号，比如alipay',
  `out_order_no` varchar(64) COMMENT '外显应用订单号（中台算法随机生成）',
  `raw_content` TEXT COMMENT '原始回调内容（JSON或XML等）',
  `parsed_data` JSON COMMENT '解析后的字段结构（可选）',
  `status` VARCHAR(32) DEFAULT 'RECEIVED' COMMENT '处理状态（RECEIVED、SUCCESS、FAILED 等）',
  `error_message` VARCHAR(255) COMMENT '错误信息（如果处理失败）',
  `notify_time` DATETIME COMMENT '回调收到时间（可提取）',
  `process_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '系统处理时间',
  `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `delsign` TINYINT DEFAULT 0 COMMENT '逻辑删除标志：0正常，1删除'
) COMMENT='支付平台回调日志表';

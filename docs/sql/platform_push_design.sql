-- --------------------------------------------------------
-- Table structure for 7 平台推送相关 
-- --------------------------------------------------------

-- CREATE TABLE `app` (
--   `id` INT  PRIMARY KEY COMMENT '应用ID',
--   `name` VARCHAR(255) NOT NULL COMMENT '应用名称',
--   `status` TINYINT NOT NULL DEFAULT 1 COMMENT '应用状态: 0=下架, 1=上架',
--   `app_key` VARCHAR(255) NOT NULL COMMENT 'app key',
--   `app_secret` VARCHAR(255) NOT NULL COMMENT 'app secret',
--   `create_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
--   `update_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
--    delsign TINYINT NOT NULL DEFAULT 0 COMMENT '删除标识: 0=未删除, 1=已删除',
--   KEY `idx_name` (`name`),
--   KEY `idx_status` (`status`)
-- ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='app基础信息表';

-- 表 app_push_cret_config 推送证书配置表
CREATE TABLE `app_push_cret_config` (
  `id` INT NOT NULL AUTO_INCREMENT COMMENT '配置ID',
  `app_id` INT NOT NULL COMMENT '应用ID',
  `device_os_id` INT NOT NULL COMMENT '设备操作系统id',
  `device_push_channel_id` INT NOT NULL COMMENT '推送渠道id',
  `environment` TINYINT NOT NULL DEFAULT 0 COMMENT '环境: 0=开发环境, 1=生产环境',
  `config` JSON NOT NULL COMMENT '推送配置相关',
  `callback_url` VARCHAR(255) COMMENT '回调url，平台推送成功回调app 如：http://app.io-game-demo.com/push/callback',
  `create_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `delsign` TINYINT NOT NULL DEFAULT 0 COMMENT '删除标识: 0=未删除, 1=已删除',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_app_type` (`app_id`, `device_push_channel_id`),
  KEY `idx_app_id` (`app_id`),
  KEY `idx_device_os` (`device_os_id`),
  KEY `idx_push_channel` (`device_push_channel_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='应用推送证书配置表';

config ios 举例
```json
{
  "app_bundle_id": "com.example.app",
  "cert_path": "/path/to/cert.p12", 
  "cert_password": "password123",
  "cert_created_at": "2024-01-01 00:00:00",
  "cert_expired_at": "2025-01-01 00:00:00"
}
```
config HUAWEI举例
```json
{
  "client_id": "your_client_id",
  "client_key": "your_client_key", 
  "client_secret": "your_client_secret",
  "content_type": "application/json",
  "package_name": "com.example.app"
}
```
CREATE TABLE `app_push_record` (
  `id` BIGINT  PRIMARY KEY AUTO_INCREMENT COMMENT '推送唯一id',
  `app_id` BIGINT  NOT NULL COMMENT '应用ID',
  `push_id` BIGINT  NOT NULL COMMENT '推送业务id',
  `device_push_channel_id` INT NOT NULL COMMENT '推送渠道id',
  `push_count` INT NOT NULL DEFAULT 0 COMMENT '推送数量',
  `status` TINYINT NOT NULL DEFAULT 0 COMMENT '0=未完成, 1=成功 2=失败',
  `fail_reason` TEXT DEFAULT NULL COMMENT '失败原因',
  `create_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `complete_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '推送完成时间',
  KEY `idx_app_id` (`app_id`),
  KEY `idx_push_id` (`push_id`),
  KEY `idx_channel_id` (`device_push_channel_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='应用推送记录表';



-- CREATE TABLE `app_push_android_config` (
--   `id` INT  PRIMARY KEY  COMMENT '配置ID',
--   `app_id` INT NOT NULL COMMENT '应用ID',
--   `device_push_channel_id` INT NOT NULL COMMENT '推送渠道id',
--   `client_id` VARCHAR(255) DEFAULT NULL COMMENT '渠道应用id',
--   `client_key` VARCHAR(255) DEFAULT NULL COMMENT '应用密钥',
--   `client_secret` VARCHAR(255) DEFAULT NULL COMMENT '应用密钥',
--   `content_type` VARCHAR(255) DEFAULT NULL COMMENT '推送请求类型',
--   `package_name` VARCHAR(255) DEFAULT NULL COMMENT '应用包名',
--   `remark` VARCHAR(255) DEFAULT NULL COMMENT '标注',
--   `create_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
--   PRIMARY KEY (`id`),
--   UNIQUE KEY `uk_game_channel` (`game_id`, `device_push_channel_id`),
--   KEY `idx_game` (`game_id`),
--   CONSTRAINT `fk_push_android_channel` FOREIGN KEY (`device_push_channel_id`) REFERENCES `device_push_channel` (`id`),
--   KEY `idx_channel` (`device_push_channel_id`)
-- ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='应用Android推送渠道配置表';

-- CREATE TABLE `app_push_ios_config` (
--   `id` INT  PRIMARY KEY COMMENT '配置ID',
--   `game_id` INT NOT NULL COMMENT '应用ID',
--   `app_id` VARCHAR(255) NOT NULL COMMENT '苹果应用Bundle ID',
--   `cert_path` TEXT NOT NULL COMMENT '推送证书路径',
--   `cert_password` VARCHAR(255) COMMENT '推送证书密码',
--   `environment` TINYINT NOT NULL DEFAULT 0 COMMENT '环境: 0=开发环境, 1=生产环境',
--   `cert_created_at` TIMESTAMP NOT NULL COMMENT '证书生成时间',
--   `cert_expired_at` TIMESTAMP NOT NULL COMMENT '证书过期时间',
--   `remark` VARCHAR(255) DEFAULT NULL COMMENT '标注',
--   `create_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
--   PRIMARY KEY (`id`),
--   UNIQUE KEY `uk_game_app` (`game_id`, `app_id`),
--   KEY `idx_game` (`game_id`),
--   KEY `idx_expired` (`cert_expired_at`)
-- ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='应用iOS推送配置表';
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>服务组列表</title>
    <style>
        table {
            width: 100%;
            border-collapse: collapse;
        }
        th, td {
            padding: 8px 12px;
            border: 1px solid #ddd;
            text-align: center;
        }
        th {
            background-color: #f2f2f2;
        }
        .status {
            padding: 5px 10px;
            border-radius: 5px;
            color: #fff;
        }
        .online {
            background-color: green;
        }
        .offline {
            background-color: red;
        }
        .running {
            background-color: blue;
        }
        .stopped {
            background-color: grey;
        }
        .restarting {
            background-color: orange;
        }
        .action-button {
            padding: 5px 10px;
            background-color: #4CAF50;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
        }
        .action-button:hover {
            background-color: #45a049;
        }
        .action-button.stop {
            background-color: red;
        }
        .action-button.stop:hover {
            background-color: #d9534f;
        }
        .action-button.restart {
            background-color: orange;
        }
        .action-button.restart:hover {
            background-color: #f0ad4e;
        }
        .action-button.status-toggle {
            background-color: #008CBA;
        }
        .action-button.status-toggle:hover {
            background-color: #007B9F;
        }
        .action-button:disabled {
            background-color: #ddd;
            cursor: not-allowed;
        }
        .edit-button {
            background-color: #ff9900;
        }
        .edit-button:hover {
            background-color: #ff7f00;
        }
        .edit-form {
            display: none;
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background-color: white;
            padding: 20px;
            border-radius: 5px;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
            width: 300px;
        }
        .edit-form input {
            width: 100%;
            padding: 8px;
            margin: 8px 0;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .edit-form button {
            padding: 10px;
            width: 100%;
            background-color: #4CAF50;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
        }
        .edit-form button:hover {
            background-color: #45a049;
        }
    </style>
</head>
<body>
<h2>服务组列表</h2>

<table>
    <thead>
    <tr>
        <th>服务组名称</th>
        <th>上下线状态</th>
        <th>运行状态</th>
        <th>操作</th>
    </tr>
    </thead>
    <tbody id="serviceGroupList">
    <!-- 动态生成的表格行 -->
    </tbody>
</table>

<!-- 编辑表单 -->
<div class="edit-form" id="editForm">
    <h3>编辑服务组</h3>
    <label for="groupName">服务组名称：</label>
    <input type="text" id="groupName" name="groupName">
    <label for="groupStatus">服务组状态：</label>
    <select id="groupStatus" name="groupStatus">
        <option value="online">上线</option>
        <option value="offline">下线</option>
    </select>
    <button onclick="saveEdits()">保存</button>
    <button onclick="closeEditForm()">取消</button>
</div>

<script>
    // 模拟数据
    const serviceGroups = [
        { name: '服务组1', onlineStatus: 'online', runStatus: 'restarting', hasStopButton: true, isRestarting: true },
        { name: '服务组2', onlineStatus: 'offline', runStatus: 'stopped', hasStopButton: true, isRestarting: false },
        { name: '服务组3', onlineStatus: 'online', runStatus: 'running', hasStopButton: false, isRestarting: false }
    ];

    let selectedServiceGroup = null;

    // 渲染服务组列表
    function renderServiceGroups() {
        const tbody = document.getElementById('serviceGroupList');
        tbody.innerHTML = ''; // 清空当前内容

        serviceGroups.forEach(serviceGroup => {
            const row = document.createElement('tr');

            // 服务组名称
            const nameCell = document.createElement('td');
            nameCell.textContent = serviceGroup.name;

            // 上下线状态
            const onlineStatusCell = document.createElement('td');
            const onlineStatusSpan = document.createElement('span');
            onlineStatusSpan.textContent = serviceGroup.onlineStatus === 'online' ? '上线' : '下线';
            onlineStatusSpan.className = 'status ' + (serviceGroup.onlineStatus === 'online' ? 'online' : 'offline');
            onlineStatusCell.appendChild(onlineStatusSpan);

            // 运行状态
            const runStatusCell = document.createElement('td');
            const runStatusSpan = document.createElement('span');
            runStatusSpan.textContent = getRunStatusText(serviceGroup.runStatus);
            runStatusSpan.className = 'status ' + getRunStatusClass(serviceGroup.runStatus);
            runStatusCell.appendChild(runStatusSpan);

            // 操作按钮
            const actionCell = document.createElement('td');

            // 上下线按钮
            const actionButtonStatusToggle = document.createElement('button');
            actionButtonStatusToggle.className = 'action-button status-toggle';
            actionButtonStatusToggle.textContent = serviceGroup.onlineStatus === 'online' ? '下线' : '上线';
            actionButtonStatusToggle.onclick = function() {
                toggleStatus(serviceGroup);
            };
            actionButtonStatusToggle.disabled = serviceGroup.runStatus === 'running'; // 运行中的不允许下线
            actionCell.appendChild(actionButtonStatusToggle);

            // 重启按钮
            const actionButtonRestart = document.createElement('button');
            actionButtonRestart.className = 'action-button restart';
            actionButtonRestart.textContent = '重启';
            actionButtonRestart.onclick = function() {
                restartService(serviceGroup);
            };
            actionButtonRestart.disabled = serviceGroup.isRestarting || serviceGroup.onlineStatus === 'offline';
            actionCell.appendChild(actionButtonRestart);

            // 停止按钮
            if (serviceGroup.hasStopButton) {
                const actionButtonStop = document.createElement('button');
                actionButtonStop.className = 'action-button stop';
                actionButtonStop.textContent = '停止';
                actionButtonStop.onclick = function() {
                    stopService(serviceGroup);
                };
                actionButtonStop.disabled = serviceGroup.isRestarting || serviceGroup.onlineStatus === 'offline' || serviceGroup.runStatus === 'running';
                actionCell.appendChild(actionButtonStop);
            }

            // 编辑按钮
            const editButton = document.createElement('button');
            editButton.className = 'action-button edit-button';
            editButton.textContent = '编辑';
            editButton.onclick = function() {
                editServiceGroup(serviceGroup);
            };
            actionCell.appendChild(editButton);

            // 添加到表格
            row.appendChild(nameCell);
            row.appendChild(onlineStatusCell);
            row.appendChild(runStatusCell);
            row.appendChild(actionCell);
            tbody.appendChild(row);
        });
    }

    // 获取运行状态文本
    function getRunStatusText(status) {
        if (status === 'restarting') return '重启中';
        return status === 'running' ? '运行中' : '停止';
    }

    // 获取运行状态对应的class
    function getRunStatusClass(status) {
        if (status === 'restarting') return 'restarting';
        return status === 'running' ? 'running' : 'stopped';
    }

    // 上下线状态切换
    function toggleStatus(serviceGroup) {
        if (serviceGroup.onlineStatus === 'online') {
            serviceGroup.onlineStatus = 'offline';
        } else {
            serviceGroup.onlineStatus = 'online';
        }
        renderServiceGroups();
    }

    // 重启服务
    function restartService(serviceGroup) {
        serviceGroup.isRestarting = true;
        console.log(`重启 ${serviceGroup.name}`);
        setTimeout(() => {
            serviceGroup.isRestarting = false;  // 模拟重启完成后，重置状态
            renderServiceGroups();
        }, 3000); // 模拟3秒钟重启时间
    }

    // 停止服务
    function stopService(serviceGroup) {
        console.log(`停止 ${serviceGroup.name}`);
        // 这里可以调用后台 API 进行停止操作
    }

    // 编辑服务组
    function editServiceGroup(serviceGroup) {
        selectedServiceGroup = serviceGroup;
        document.getElementById('groupName').value = serviceGroup.name;
        document.getElementById('groupStatus').value = serviceGroup.onlineStatus;
        document.getElementById('editForm').style.display = 'block';
    }

    // 保存编辑
    function saveEdits() {
        selectedServiceGroup.name = document.getElementById('groupName').value;
        selectedServiceGroup.onlineStatus = document.getElementById('groupStatus').value;
        closeEditForm();
        renderServiceGroups();
    }

    // 关闭编辑表单
    function closeEditForm() {
        document.getElementById('editForm').style.display = 'none';
    }

    // 初始化页面
    renderServiceGroups();
</script>
</body>
</html>

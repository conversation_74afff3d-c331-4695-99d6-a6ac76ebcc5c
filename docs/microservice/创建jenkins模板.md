# jenkin模板制作流程

## 获取网页上job的config.xml

这里使用预设好统一的token就行

如果想用自己的--user就换成自己的，个人->Configure->API Token生成保存即可

```shell
curl "http://**************:9999/job/{jobname}/config.xml" --user bot1:11f3760b83b57bd3b41494c80737de2b31
```
![image-20250722141345217.png](image-20250722141345217.png)
红框里面的就是你的jobname

## 挖坑

以vpn-cloud-test为例，curl返回原始完整内容

```xml
<?xml version='1.1' encoding='UTF-8'?>
<project>
  <actions/>
  <description></description>
  <keepDependencies>false</keepDependencies>
  <properties>
    <jenkins.model.BuildDiscarderProperty>
      <strategy class="hudson.tasks.LogRotator">
        <daysToKeep>-1</daysToKeep>
        <numToKeep>4</numToKeep>
        <artifactDaysToKeep>-1</artifactDaysToKeep>
        <artifactNumToKeep>-1</artifactNumToKeep>
      </strategy>
    </jenkins.model.BuildDiscarderProperty>
    <com.dabsquared.gitlabjenkins.connection.GitLabConnectionProperty plugin="gitlab-plugin@1.8.0">
      <gitLabConnection></gitLabConnection>
      <jobCredentialId></jobCredentialId>
      <useAlternativeCredential>false</useAlternativeCredential>
    </com.dabsquared.gitlabjenkins.connection.GitLabConnectionProperty>
    <org.jenkinsci.plugins.gitlablogo.GitlabLogoProperty plugin="gitlab-logo@130.v9d2696eb_8dc6">
      <repositoryName></repositoryName>
    </org.jenkinsci.plugins.gitlablogo.GitlabLogoProperty>
    <hudson.model.ParametersDefinitionProperty>
      <parameterDefinitions>
        <hudson.model.ChoiceParameterDefinition>
          <name>p1</name>
          <description>重启开始停止</description>
          <choices class="java.util.Arrays$ArrayList">
            <a class="string-array">
              <string>restart</string>
              <string>start</string>
              <string>stop</string>
            </a>
          </choices>
        </hudson.model.ChoiceParameterDefinition>
        <hudson.model.ChoiceParameterDefinition>
          <name>p2</name>
          <description>服务名</description>
          <choices class="java.util.Arrays$ArrayList">
            <a class="string-array">
              <string>account</string>
              <string>exchange</string>
              <string>router</string>
              <string>admin</string>
            </a>
          </choices>
        </hudson.model.ChoiceParameterDefinition>
        <hudson.model.ChoiceParameterDefinition>
          <name>p3</name>
          <description>环境</description>
          <choices class="java.util.Arrays$ArrayList">
            <a class="string-array">
              <string>test</string>
            </a>
          </choices>
        </hudson.model.ChoiceParameterDefinition>
        <org.biouno.unochoice.CascadeChoiceParameter plugin="uno-choice@2.8.4">
          <name>p4</name>
          <description>端口号</description>
          <randomName>choice-parameter-**************</randomName>
          <visibleItemCount>1</visibleItemCount>
          <script class="org.biouno.unochoice.model.GroovyScript">
            <secureScript plugin="script-security@1369.v9b_98a_4e95b_2d">
              <script>if (p2.equals(&quot;exchange&quot;) &amp;&amp; p3.equals(&quot;test&quot;)) {
    return [&quot;8919&quot;]
}  else if (p2.equals(&quot;account&quot;) &amp;&amp; p3.equals(&quot;test&quot;)) {
    return [&quot;8918&quot;]
} else if (p2.equals(&quot;admin&quot;) &amp;&amp; p3.equals(&quot;test&quot;)) {
    return [&quot;8917&quot;]
} else if (p2.equals(&quot;router&quot;) &amp;&amp; p3.equals(&quot;test&quot;)) {
    return [&quot;8916&quot;]
} </script>
              <sandbox>false</sandbox>
            </secureScript>
            <secureFallbackScript plugin="script-security@1369.v9b_98a_4e95b_2d">
              <script></script>
              <sandbox>false</sandbox>
            </secureFallbackScript>
          </script>
          <projectName>vpn-cloud-test</projectName>
          <projectFullName>vpn-cloud-test</projectFullName>
          <parameters class="linked-hash-map"/>
          <referencedParameters>p2,p3</referencedParameters>
          <choiceType>PT_SINGLE_SELECT</choiceType>
          <filterable>false</filterable>
          <filterLength>1</filterLength>
        </org.biouno.unochoice.CascadeChoiceParameter>
        <net.uaznia.lukanus.hudson.plugins.gitparameter.GitParameterDefinition plugin="git-parameter@0.10.0">
          <name>p5</name>
          <description>Git分支</description>
          <uuid>4456e073-b381-4c35-94c6-9b2c440a0158</uuid>
          <type>PT_BRANCH</type>
          <branch></branch>
          <tagFilter>*</tagFilter>
          <branchFilter>origin/(.*)</branchFilter>
          <sortMode>NONE</sortMode>
          <defaultValue>test</defaultValue>
          <selectedValue>NONE</selectedValue>
          <quickFilterEnabled>false</quickFilterEnabled>
          <listSize>5</listSize>
          <requiredParameter>false</requiredParameter>
        </net.uaznia.lukanus.hudson.plugins.gitparameter.GitParameterDefinition>
        <hudson.model.ChoiceParameterDefinition>
          <name>p6</name>
          <description>内存</description>
          <choices class="java.util.Arrays$ArrayList">
            <a class="string-array">
              <string>512m</string>
              <string>1024m</string>
            </a>
          </choices>
        </hudson.model.ChoiceParameterDefinition>
        <hudson.model.ChoiceParameterDefinition>
          <name>p7</name>
          <description>harbor地址</description>
          <choices class="java.util.Arrays$ArrayList">
            <a class="string-array">
              <string>**************:1180</string>
            </a>
          </choices>
        </hudson.model.ChoiceParameterDefinition>
        <hudson.model.StringParameterDefinition>
          <name>p9</name>
          <defaultValue>test</defaultValue>
          <trim>false</trim>
        </hudson.model.StringParameterDefinition>
      </parameterDefinitions>
    </hudson.model.ParametersDefinitionProperty>
  </properties>
  <scm class="hudson.plugins.git.GitSCM" plugin="git@5.5.2">
    <configVersion>2</configVersion>
    <userRemoteConfigs>
      <hudson.plugins.git.UserRemoteConfig>
        <url>http://gitlab.53site.com/vpn/vpn-cloud.git</url>
        <credentialsId>gitlab</credentialsId>
      </hudson.plugins.git.UserRemoteConfig>
    </userRemoteConfigs>
    <branches>
      <hudson.plugins.git.BranchSpec>
        <name>${p5}</name>
      </hudson.plugins.git.BranchSpec>
    </branches>
    <doGenerateSubmoduleConfigurations>false</doGenerateSubmoduleConfigurations>
    <submoduleCfg class="empty-list"/>
    <extensions/>
  </scm>
  <canRoam>true</canRoam>
  <disabled>false</disabled>
  <blockBuildWhenDownstreamBuilding>false</blockBuildWhenDownstreamBuilding>
  <blockBuildWhenUpstreamBuilding>false</blockBuildWhenUpstreamBuilding>
  <jdk>Java11</jdk>
  <triggers/>
  <concurrentBuild>false</concurrentBuild>
  <builders>
    <hudson.tasks.Maven>
      <targets>clean deploy -pl com.mega.vpn.cloud:vpn-cloud-${p2} -am  -Dmaven.test.skip=true -Dgit.branch=${p5} -Dharbor.url=${p7}</targets>
      <mavenName>maven-3.8.1</mavenName>
      <usePrivateRepository>false</usePrivateRepository>
      <settings class="jenkins.mvn.DefaultSettingsProvider"/>
      <globalSettings class="jenkins.mvn.DefaultGlobalSettingsProvider"/>
      <injectBuildVariables>false</injectBuildVariables>
    </hudson.tasks.Maven>
    <jenkins.plugins.publish__over__ssh.BapSshBuilderPlugin plugin="publish-over-ssh@383.v4eb_4c44da_2dd">
      <delegate>
        <consolePrefix>SSH: </consolePrefix>
        <delegate plugin="publish-over@0.22">
          <publishers>
            <jenkins.plugins.publish__over__ssh.BapSshPublisher plugin="publish-over-ssh@383.v4eb_4c44da_2dd">
              <configName>HZ-Test</configName>
              <verbose>false</verbose>
              <transfers>
                <jenkins.plugins.publish__over__ssh.BapSshTransfer>
                  <remoteDirectory></remoteDirectory>
                  <sourceFiles>vpn-cloud.sh</sourceFiles>
                  <excludes></excludes>
                  <removePrefix></removePrefix>
                  <remoteDirectorySDF>false</remoteDirectorySDF>
                  <flatten>false</flatten>
                  <cleanRemote>false</cleanRemote>
                  <noDefaultExcludes>false</noDefaultExcludes>
                  <makeEmptyDirs>false</makeEmptyDirs>
                  <patternSeparator>[, ]+</patternSeparator>
                  <execCommand>mv -f vpn-cloud.sh /opt
chmod 777 /opt/vpn-cloud.sh
/bin/bash /opt/vpn-cloud.sh ${p1} ${p2} ${p3} ${p4} ${p5} ${p6} ${p7}</execCommand>
                  <execTimeout>120000</execTimeout>
                  <usePty>false</usePty>
                  <useAgentForwarding>false</useAgentForwarding>
                  <useSftpForExec>false</useSftpForExec>
                  <keepFilePermissions>false</keepFilePermissions>
                </jenkins.plugins.publish__over__ssh.BapSshTransfer>
              </transfers>
              <useWorkspaceInPromotion>false</useWorkspaceInPromotion>
              <usePromotionTimestamp>false</usePromotionTimestamp>
            </jenkins.plugins.publish__over__ssh.BapSshPublisher>
          </publishers>
          <continueOnError>false</continueOnError>
          <failOnError>false</failOnError>
          <alwaysPublishFromMaster>false</alwaysPublishFromMaster>
          <hostConfigurationAccess class="jenkins.plugins.publish_over_ssh.BapSshPublisherPlugin" reference="../.."/>
        </delegate>
      </delegate>
    </jenkins.plugins.publish__over__ssh.BapSshBuilderPlugin>
  </builders>
  <publishers/>
  <buildWrappers/>
</project>
```

### 1.通用规则

1. 关键字规则

   sshServerName：发布目标机器

   activeProfile：环境标识 dev | test | beta | prod | ...

   restartType：启动方式 restart | stop | start

   port：占用端口

   关键字不是必须有，但是如果有这个需求，就必须用这个关键字

2. 所有和服务组有关的，整个服务组参数相同的，属于静态参数，使用@key@这种方式，在创建JenkinsJob的时候会统一赋值一次。所有和服务有关的，每个服务传值可能不同的，属于动态参数，使用${key}这种方式，在build的时候作为动态参数传给jenkins

3. 如果参数是静态参数，则可以直接把这个标签去了，如果参数是动态参数，在页面上是下拉选项的（<hudson.model.ChoiceParameterDefinition>）、groovy动态计算的（org.biouno.unochoice.CascadeChoiceParameter plugin="uno-choice@2.8.4"）、统一修改成字符串输入（hudson.model.StringParameterDefinition），页面下拉只是约定可选值，本质也是把key value传给jenkins。同时注意替换下面的引用



### 2.parameter部分

#### p1-启动方式-restartType

每次启动操作不一定，属于动态参数

- 根据规则1，启动方式属于关键字必须要用restartType作为占位符
- 根据规则2，动态参数，要用${restartType}占位
- 根据规则3，动态参数，把下拉标签替换成字符串标签，下面的引用也要改

添加restartType标签，${p1}替换成${restartType}

```xml
<hudson.model.ChoiceParameterDefinition>
  <name>p1</name>
  <description>重启开始停止</description>
  <choices class="java.util.Arrays$ArrayList">
    <a class="string-array">
      <string>restart</string>
      <string>start</string>
      <string>stop</string>
    </a>
  </choices>
</hudson.model.ChoiceParameterDefinition>
```

```xml
<hudson.model.StringParameterDefinition>
  <name>restartType</name>
  <defaultValue>restart</defaultValue> -- 可加可不加
  <trim>false</trim>
</hudson.model.StringParameterDefinition>
```

#### p2-服务名-moduleName

这个jenkins是将所有模块放在一起，但在平台系统里每个模块都是一个JenkinsJob，所以属于静态参数

- 根据规则1，不是关键字，名字随意
- 根据规则2，静态参数，要用@moduleName@占位，
- 根据规则3，静态参数，删掉标签，但下面的引用要改

删除p2标签，${p2}替换成@moduleName@

#### p3-环境-activeProfile

不同环境应该创建不同的服务组，所以属于静态参数

- 根据规则1，环境属于关键字必须要用activeProfile作为占位符
- 根据规则2，静态参数，要用@activeProfile@占位，
- 根据规则3，静态参数，删掉标签，但下面的引用要改

删除p3标签，${p3}替换成@activeProfile@

#### p4-端口-port

根据目前设计同一机器不同端口的服务可复用同一个JenkinsJob，不同机器一定属于不同的JenkinsJob，所以属于动态参数

- 根据规则1，环境属于关键字必须要用port作为占位符
- 根据规则2，动态参数，要用${port}占位
- 根据规则3，动态参数，把下拉标签替换成字符串标签，下面的引用也要改

添加port标签，${p4}替换成${port}

```xml
<org.biouno.unochoice.CascadeChoiceParameter plugin="uno-choice@2.8.4">
  <name>p4</name>
  <description>端口号</description>
  <randomName>choice-parameter-**************</randomName>
  <visibleItemCount>1</visibleItemCount>
  <script class="org.biouno.unochoice.model.GroovyScript">
    <secureScript plugin="script-security@1369.v9b_98a_4e95b_2d">
      <script>if (p2.equals(&quot;exchange&quot;) &amp;&amp; p3.equals(&quot;test&quot;)) {
return [&quot;8919&quot;]
}  else if (p2.equals(&quot;account&quot;) &amp;&amp; p3.equals(&quot;test&quot;)) {
return [&quot;8918&quot;]
} else if (p2.equals(&quot;admin&quot;) &amp;&amp; p3.equals(&quot;test&quot;)) {
return [&quot;8917&quot;]
} else if (p2.equals(&quot;router&quot;) &amp;&amp; p3.equals(&quot;test&quot;)) {
return [&quot;8916&quot;]
} </script>
      <sandbox>false</sandbox>
    </secureScript>
    <secureFallbackScript plugin="script-security@1369.v9b_98a_4e95b_2d">
      <script></script>
      <sandbox>false</sandbox>
    </secureFallbackScript>
  </script>
  <projectName>vpn-cloud-test</projectName>
  <projectFullName>vpn-cloud-test</projectFullName>
  <parameters class="linked-hash-map"/>
  <referencedParameters>p2,p3</referencedParameters>
  <choiceType>PT_SINGLE_SELECT</choiceType>
  <filterable>false</filterable>
  <filterLength>1</filterLength>
</org.biouno.unochoice.CascadeChoiceParameter>
```

```xml
<hudson.model.StringParameterDefinition>
  <name>port</name>
  <trim>false</trim>
</hudson.model.StringParameterDefinition>
```

#### p5-git分支-gitBranch

不同git分支应该创建不同的服务组，所以属于静态参数

- 根据规则1，不是关键字，名字随意
- 根据规则2，静态参数，要用@gitBranch@占位，
- 根据规则3，静态参数，删掉标签，但下面的引用要改

删除p5标签，${p5}替换成@gitBranch@

#### p6-内存-mem

内存这种为了灵活设，设计成动态参数带默认值的方式，如果设计成静态参数的话，配置一次就不能修改了

- 根据规则1，不是关键字，名字随意
- 根据规则2，动态参数，要用${mem}占位
- 根据规则3，动态参数，把下拉标签替换成字符串标签，下面的引用也要改

添加mem标签，${p6}替换成${mem}

```xml
<hudson.model.ChoiceParameterDefinition>
  <name>p6</name>
  <description>内存</description>
  <choices class="java.util.Arrays$ArrayList">
    <a class="string-array">
      <string>512m</string>
      <string>1024m</string>
    </a>
  </choices>
</hudson.model.ChoiceParameterDefinition>
```

```xml
<hudson.model.StringParameterDefinition>
  <name>mem</name>
  <trim>false</trim>
</hudson.model.StringParameterDefinition>
```

#### p7-harbor地址-harbor

harbot这种属于纯后台参数，设计成静态参数带默认值且默认不可修改的方式

- 根据规则1，不是关键字，名字随意
- 根据规则2，静态参数，要用@harbor@占位，
- 根据规则3，静态参数，删掉标签，但下面的引用要改

删除p7标签，${p7}替换成@harbor@

#### 此时配置文件

```xml
<?xml version='1.1' encoding='UTF-8'?>
<project>
  <actions/>
  <description></description>
  <keepDependencies>false</keepDependencies>
  <properties>
    <jenkins.model.BuildDiscarderProperty>
      <strategy class="hudson.tasks.LogRotator">
        <daysToKeep>-1</daysToKeep>
        <numToKeep>4</numToKeep>
        <artifactDaysToKeep>-1</artifactDaysToKeep>
        <artifactNumToKeep>-1</artifactNumToKeep>
      </strategy>
    </jenkins.model.BuildDiscarderProperty>
    <com.dabsquared.gitlabjenkins.connection.GitLabConnectionProperty plugin="gitlab-plugin@1.8.0">
      <gitLabConnection></gitLabConnection>
      <jobCredentialId></jobCredentialId>
      <useAlternativeCredential>false</useAlternativeCredential>
    </com.dabsquared.gitlabjenkins.connection.GitLabConnectionProperty>
    <org.jenkinsci.plugins.gitlablogo.GitlabLogoProperty plugin="gitlab-logo@130.v9d2696eb_8dc6">
      <repositoryName></repositoryName>
    </org.jenkinsci.plugins.gitlablogo.GitlabLogoProperty>
    <hudson.model.ParametersDefinitionProperty>
      <parameterDefinitions>
        <hudson.model.StringParameterDefinition>
          <name>restartType</name>
          <defaultValue>restart</defaultValue>
          <trim>false</trim>
        </hudson.model.StringParameterDefinition>
        <hudson.model.StringParameterDefinition>
          <name>port</name>
          <trim>false</trim>
        </hudson.model.StringParameterDefinition>
        <hudson.model.StringParameterDefinition>
          <name>mem</name>
          <trim>false</trim>
        </hudson.model.StringParameterDefinition>
      </parameterDefinitions>
    </hudson.model.ParametersDefinitionProperty>
  </properties>
  <scm class="hudson.plugins.git.GitSCM" plugin="git@5.5.2">
    <configVersion>2</configVersion>
    <userRemoteConfigs>
      <hudson.plugins.git.UserRemoteConfig>
        <url>http://gitlab.53site.com/vpn/vpn-cloud.git</url>
        <credentialsId>gitlab</credentialsId>
      </hudson.plugins.git.UserRemoteConfig>
    </userRemoteConfigs>
    <branches>
      <hudson.plugins.git.BranchSpec>
        <name>@gitBranch@</name>
      </hudson.plugins.git.BranchSpec>
    </branches>
    <doGenerateSubmoduleConfigurations>false</doGenerateSubmoduleConfigurations>
    <submoduleCfg class="empty-list"/>
    <extensions/>
  </scm>
  <canRoam>true</canRoam>
  <disabled>false</disabled>
  <blockBuildWhenDownstreamBuilding>false</blockBuildWhenDownstreamBuilding>
  <blockBuildWhenUpstreamBuilding>false</blockBuildWhenUpstreamBuilding>
  <jdk>Java11</jdk>
  <triggers/>
  <concurrentBuild>false</concurrentBuild>
  <builders>
    <hudson.tasks.Maven>
      <targets>clean deploy -pl com.mega.vpn.cloud:vpn-cloud-@moduleName@ -am  -Dmaven.test.skip=true -Dgit.branch=@gitBranch@ -Dharbor.url=@harbor@</targets>
      <mavenName>maven-3.8.1</mavenName>
      <usePrivateRepository>false</usePrivateRepository>
      <settings class="jenkins.mvn.DefaultSettingsProvider"/>
      <globalSettings class="jenkins.mvn.DefaultGlobalSettingsProvider"/>
      <injectBuildVariables>false</injectBuildVariables>
    </hudson.tasks.Maven>
    <jenkins.plugins.publish__over__ssh.BapSshBuilderPlugin plugin="publish-over-ssh@383.v4eb_4c44da_2dd">
      <delegate>
        <consolePrefix>SSH: </consolePrefix>
        <delegate plugin="publish-over@0.22">
          <publishers>
            <jenkins.plugins.publish__over__ssh.BapSshPublisher plugin="publish-over-ssh@383.v4eb_4c44da_2dd">
              <configName>HZ-Test</configName>
              <verbose>false</verbose>
              <transfers>
                <jenkins.plugins.publish__over__ssh.BapSshTransfer>
                  <remoteDirectory></remoteDirectory>
                  <sourceFiles>vpn-cloud.sh</sourceFiles>
                  <excludes></excludes>
                  <removePrefix></removePrefix>
                  <remoteDirectorySDF>false</remoteDirectorySDF>
                  <flatten>false</flatten>
                  <cleanRemote>false</cleanRemote>
                  <noDefaultExcludes>false</noDefaultExcludes>
                  <makeEmptyDirs>false</makeEmptyDirs>
                  <patternSeparator>[, ]+</patternSeparator>
                  <execCommand>mv -f vpn-cloud.sh /opt
chmod 777 /opt/vpn-cloud.sh
/bin/bash /opt/vpn-cloud.sh ${restartType} @moduleName@ @activeProfile@ ${port} @gitBranch@ ${mem} @harbor@</execCommand>
                  <execTimeout>120000</execTimeout>
                  <usePty>false</usePty>
                  <useAgentForwarding>false</useAgentForwarding>
                  <useSftpForExec>false</useSftpForExec>
                  <keepFilePermissions>false</keepFilePermissions>
                </jenkins.plugins.publish__over__ssh.BapSshTransfer>
              </transfers>
              <useWorkspaceInPromotion>false</useWorkspaceInPromotion>
              <usePromotionTimestamp>false</usePromotionTimestamp>
            </jenkins.plugins.publish__over__ssh.BapSshPublisher>
          </publishers>
          <continueOnError>false</continueOnError>
          <failOnError>false</failOnError>
          <alwaysPublishFromMaster>false</alwaysPublishFromMaster>
          <hostConfigurationAccess class="jenkins.plugins.publish_over_ssh.BapSshPublisherPlugin" reference="../.."/>
        </delegate>
      </delegate>
    </jenkins.plugins.publish__over__ssh.BapSshBuilderPlugin>
  </builders>
  <publishers/>
  <buildWrappers/>
</project>
```

### 3.其他配置

其他配置大概率都属于静态参数，反正都遵循通用规则替换参数

#### git

git地址：gitUrl

```xml
<hudson.plugins.git.UserRemoteConfig>
  <url>http://gitlab.53site.com/vpn/vpn-cloud.git</url>
  <credentialsId>gitlab</credentialsId>
</hudson.plugins.git.UserRemoteConfig>
```

```xml
<hudson.plugins.git.UserRemoteConfig>
  <url>@gitUrl@</url>
  <credentialsId>gitlab</credentialsId>
</hudson.plugins.git.UserRemoteConfig>
```

#### maven

包名前缀：groupId

appName：appName

```xml
<hudson.tasks.Maven>
  <targets>clean deploy -pl com.mega.vpn.cloud:vpn-cloud-@moduleName@ -am  -Dmaven.test.skip=true -Dgit.branch=@gitBranch@ -Dharbor.url=@harbor@</targets>
  <mavenName>maven-3.8.1</mavenName>
  <usePrivateRepository>false</usePrivateRepository>
  <settings class="jenkins.mvn.DefaultSettingsProvider"/>
  <globalSettings class="jenkins.mvn.DefaultGlobalSettingsProvider"/>
  <injectBuildVariables>false</injectBuildVariables>
</hudson.tasks.Maven>
```

```xml
<hudson.tasks.Maven>
  <targets>clean deploy -pl ${groupId}:${appName}-@moduleName@ -am  -Dmaven.test.skip=true -Dgit.branch=@gitBranch@ -Dharbor.url=@harbor@</targets>
  <mavenName>maven-3.8.1</mavenName>
  <usePrivateRepository>false</usePrivateRepository>
  <settings class="jenkins.mvn.DefaultSettingsProvider"/>
  <globalSettings class="jenkins.mvn.DefaultGlobalSettingsProvider"/>
  <injectBuildVariables>false</injectBuildVariables>
</hudson.tasks.Maven>
```

#### ssh publisher

发布目标机器：根据规则1，发布目标机器属于关键字必须要用sshServerName作为占位符

执行脚本名：cloudRestartFileName

```xml
<publishers>
  <jenkins.plugins.publish__over__ssh.BapSshPublisher plugin="publish-over-ssh@383.v4eb_4c44da_2dd">
    <configName>HZ-Test</configName>
    <verbose>false</verbose>
    <transfers>
      <jenkins.plugins.publish__over__ssh.BapSshTransfer>
        <remoteDirectory></remoteDirectory>
        <sourceFiles>vpn-cloud.sh</sourceFiles>
        <excludes></excludes>
        <removePrefix></removePrefix>
        <remoteDirectorySDF>false</remoteDirectorySDF>
        <flatten>false</flatten>
        <cleanRemote>false</cleanRemote>
        <noDefaultExcludes>false</noDefaultExcludes>
        <makeEmptyDirs>false</makeEmptyDirs>
        <patternSeparator>[, ]+</patternSeparator>
        <execCommand>mv -f vpn-cloud.sh /opt
chmod 777 /opt/vpn-cloud.sh
/bin/bash /opt/vpn-cloud.sh ${restartType} @moduleName@ @activeProfile@ ${port} @gitBranch@ ${mem} @harbor@</execCommand>
        <execTimeout>120000</execTimeout>
        <usePty>false</usePty>
        <useAgentForwarding>false</useAgentForwarding>
        <useSftpForExec>false</useSftpForExec>
        <keepFilePermissions>false</keepFilePermissions>
      </jenkins.plugins.publish__over__ssh.BapSshTransfer>
    </transfers>
    <useWorkspaceInPromotion>false</useWorkspaceInPromotion>
    <usePromotionTimestamp>false</usePromotionTimestamp>
  </jenkins.plugins.publish__over__ssh.BapSshPublisher>
</publishers>
```

```xml
<publishers>
  <jenkins.plugins.publish__over__ssh.BapSshPublisher plugin="publish-over-ssh@383.v4eb_4c44da_2dd">
    <configName>@sshServerName@</configName>
    <verbose>false</verbose>
    <transfers>
      <jenkins.plugins.publish__over__ssh.BapSshTransfer>
        <remoteDirectory></remoteDirectory>
        <sourceFiles>@cloudRestartFileName@</sourceFiles>
        <excludes></excludes>
        <removePrefix></removePrefix>
        <remoteDirectorySDF>false</remoteDirectorySDF>
        <flatten>false</flatten>
        <cleanRemote>false</cleanRemote>
        <noDefaultExcludes>false</noDefaultExcludes>
        <makeEmptyDirs>false</makeEmptyDirs>
        <patternSeparator>[, ]+</patternSeparator>
        <execCommand>mv -f @cloudRestartFileName@ /opt
chmod 777 /opt/@cloudRestartFileName@
/bin/bash /opt/@cloudRestartFileName@ ${restartType} @moduleName@ @activeProfile@ ${port} @gitBranch@ ${mem} @harbor@</execCommand>
        <execTimeout>120000</execTimeout>
        <usePty>false</usePty>
        <useAgentForwarding>false</useAgentForwarding>
        <useSftpForExec>false</useSftpForExec>
        <keepFilePermissions>false</keepFilePermissions>
      </jenkins.plugins.publish__over__ssh.BapSshTransfer>
    </transfers>
    <useWorkspaceInPromotion>false</useWorkspaceInPromotion>
    <usePromotionTimestamp>false</usePromotionTimestamp>
  </jenkins.plugins.publish__over__ssh.BapSshPublisher>
</publishers>
```

#### 此时配置文件

如果有其他流程继续按照规则挖坑，如果没有其他流程，此时的xml就是最终的模板

```xml
<?xml version='1.1' encoding='UTF-8'?>
<project>
  <actions/>
  <description></description>
  <keepDependencies>false</keepDependencies>
  <properties>
    <jenkins.model.BuildDiscarderProperty>
      <strategy class="hudson.tasks.LogRotator">
        <daysToKeep>-1</daysToKeep>
        <numToKeep>4</numToKeep>
        <artifactDaysToKeep>-1</artifactDaysToKeep>
        <artifactNumToKeep>-1</artifactNumToKeep>
      </strategy>
    </jenkins.model.BuildDiscarderProperty>
    <com.dabsquared.gitlabjenkins.connection.GitLabConnectionProperty plugin="gitlab-plugin@1.8.0">
      <gitLabConnection></gitLabConnection>
      <jobCredentialId></jobCredentialId>
      <useAlternativeCredential>false</useAlternativeCredential>
    </com.dabsquared.gitlabjenkins.connection.GitLabConnectionProperty>
    <org.jenkinsci.plugins.gitlablogo.GitlabLogoProperty plugin="gitlab-logo@130.v9d2696eb_8dc6">
      <repositoryName></repositoryName>
    </org.jenkinsci.plugins.gitlablogo.GitlabLogoProperty>
    <hudson.model.ParametersDefinitionProperty>
      <parameterDefinitions>
        <hudson.model.StringParameterDefinition>
          <name>restartType</name>
          <defaultValue>restart</defaultValue>
          <trim>false</trim>
        </hudson.model.StringParameterDefinition>
        <hudson.model.StringParameterDefinition>
          <name>port</name>
          <trim>false</trim>
        </hudson.model.StringParameterDefinition>
        <hudson.model.StringParameterDefinition>
          <name>mem</name>
          <trim>false</trim>
        </hudson.model.StringParameterDefinition>
      </parameterDefinitions>
    </hudson.model.ParametersDefinitionProperty>
  </properties>
  <scm class="hudson.plugins.git.GitSCM" plugin="git@5.5.2">
    <configVersion>2</configVersion>
    <userRemoteConfigs>
      <hudson.plugins.git.UserRemoteConfig>
        <url>@gitUrl@</url>
        <credentialsId>gitlab</credentialsId>
      </hudson.plugins.git.UserRemoteConfig>
    </userRemoteConfigs>
    <branches>
      <hudson.plugins.git.BranchSpec>
        <name>@gitBranch@</name>
      </hudson.plugins.git.BranchSpec>
    </branches>
    <doGenerateSubmoduleConfigurations>false</doGenerateSubmoduleConfigurations>
    <submoduleCfg class="empty-list"/>
    <extensions/>
  </scm>
  <canRoam>true</canRoam>
  <disabled>false</disabled>
  <blockBuildWhenDownstreamBuilding>false</blockBuildWhenDownstreamBuilding>
  <blockBuildWhenUpstreamBuilding>false</blockBuildWhenUpstreamBuilding>
  <jdk>Java11</jdk>
  <triggers/>
  <concurrentBuild>false</concurrentBuild>
  <builders>
    <hudson.tasks.Maven>
      <targets>clean deploy -pl ${groupId}:${appName}-@moduleName@ -am  -Dmaven.test.skip=true -Dgit.branch=@gitBranch@ -Dharbor.url=@harbor@</targets>
      <mavenName>maven-3.8.1</mavenName>
      <usePrivateRepository>false</usePrivateRepository>
      <settings class="jenkins.mvn.DefaultSettingsProvider"/>
      <globalSettings class="jenkins.mvn.DefaultGlobalSettingsProvider"/>
      <injectBuildVariables>false</injectBuildVariables>
    </hudson.tasks.Maven>
    <jenkins.plugins.publish__over__ssh.BapSshBuilderPlugin plugin="publish-over-ssh@383.v4eb_4c44da_2dd">
      <delegate>
        <consolePrefix>SSH: </consolePrefix>
        <delegate plugin="publish-over@0.22">
          <publishers>
            <jenkins.plugins.publish__over__ssh.BapSshPublisher plugin="publish-over-ssh@383.v4eb_4c44da_2dd">
              <configName>@sshServerName@</configName>
              <verbose>false</verbose>
              <transfers>
                <jenkins.plugins.publish__over__ssh.BapSshTransfer>
                  <remoteDirectory></remoteDirectory>
                  <sourceFiles>@cloudRestartFileName@</sourceFiles>
                  <excludes></excludes>
                  <removePrefix></removePrefix>
                  <remoteDirectorySDF>false</remoteDirectorySDF>
                  <flatten>false</flatten>
                  <cleanRemote>false</cleanRemote>
                  <noDefaultExcludes>false</noDefaultExcludes>
                  <makeEmptyDirs>false</makeEmptyDirs>
                  <patternSeparator>[, ]+</patternSeparator>
                  <execCommand>mv -f @cloudRestartFileName@ /opt
chmod 777 /opt/@cloudRestartFileName@
/bin/bash /opt/@cloudRestartFileName@ ${restartType} @moduleName@ @activeProfile@ ${port} @gitBranch@ ${mem} @harbor@</execCommand>
                  <execTimeout>120000</execTimeout>
                  <usePty>false</usePty>
                  <useAgentForwarding>false</useAgentForwarding>
                  <useSftpForExec>false</useSftpForExec>
                  <keepFilePermissions>false</keepFilePermissions>
                </jenkins.plugins.publish__over__ssh.BapSshTransfer>
              </transfers>
              <useWorkspaceInPromotion>false</useWorkspaceInPromotion>
              <usePromotionTimestamp>false</usePromotionTimestamp>
            </jenkins.plugins.publish__over__ssh.BapSshPublisher>
          </publishers>
          <continueOnError>false</continueOnError>
          <failOnError>false</failOnError>
          <alwaysPublishFromMaster>false</alwaysPublishFromMaster>
          <hostConfigurationAccess class="jenkins.plugins.publish_over_ssh.BapSshPublisherPlugin" reference="../.."/>
        </delegate>
      </delegate>
    </jenkins.plugins.publish__over__ssh.BapSshBuilderPlugin>
  </builders>
  <publishers/>
  <buildWrappers/>
</project>
```



## 参数保存数据库

#### jenkins_job_template

template_name：给模板起一个名字

xml_content：上面的xml模板

support_stop：0-只支持restart，1-支持restart和stop。

#### jenkins_job_template_param

jenkins_template_id：jenkins_job_template表id

param_key：占位符 不包含${} 和@@

param_name：给参数起一个名字，会返回给前端

services_data_type：1-创建服务组阶段需要填写的 2-创建服务阶段需要填写的

required：是否必填 0-必填 1-非必填，非必填情况下请写入default_value，默认参数返回给前端时携带默认值

default_value：非必填时参数的默认值

is_visible：0-后台参数 1-普通参数，只有普通参数才返回给前端，后台参数一定要是非必填且有默认值












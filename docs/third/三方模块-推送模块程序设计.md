# 推送模块程序设计

## 需求
本服务只提供一个路由接口，调用者发送要推送的内容：
1 推送的渠道比如是苹果还是华为
2 平台的配置，比如苹果或者华为的配置
3 推送标题和内容
4 推送的push_token列表
5 推送的app id 
请求携带token

在接口校验完token后,内部对push_token按200一组存入渠道对应的redis队列，定时器负责从redis出队，调用不同的推送渠道的实现。推送成功，记录写入数据库

## 流程图

```mermaid
flowchart TD
    A[调用者] --> B[发送推送请求]
    B --> C[推送服务接收请求]
    C --> D{验证Token}
    D -->|验证失败| E[返回错误信息]
    D -->|验证成功| F[解析推送参数]
    F --> G[按渠道分类处理]
    G --> H[将push_token按200一组分批]
    H --> I[存入对应渠道的Redis队列]
    I -->|存储成功| J[返回接收成功信息]
    I -->|存储失败| K[返回处理失败信息]
    
    L[定时器] --> M{检查Redis队列}
    M -->|队列为空| L
    M -->|队列有数据| N[从Redis队列出队]
    N --> O{判断推送渠道}
    O -->|苹果| P[调用苹果推送实现]
    O -->|华为| Q[调用华为推送实现]
    O -->|其他渠道| R[调用对应渠道实现]
    
    P --> S{推送结果}
    Q --> S
    R --> S
    S -->|推送成功| T[记录成功结果到数据库]
    S -->|推送失败| U[记录失败结果到数据库]
    T --> V[清理资源]
    U --> V
    V --> L
```

## 时序图

```mermaid
sequenceDiagram
    participant C as 调用者
    participant PS as 推送服务API
    participant TV as Token验证服务
    participant RQ as Redis队列
    participant TS as 定时器服务
    participant AP as 苹果推送实现
    participant HP as 华为推送实现
    participant OP as 其他推送实现
    participant DB as 数据库
    
    C->>PS: 发送推送请求(渠道,配置,标题内容,push_token列表,app id,token)
    PS->>TV: 验证token
    
    alt token验证成功
        TV-->>PS: 验证成功
        PS->>PS: 解析推送参数
        PS->>PS: 按渠道分类处理
        PS->>PS: 将push_token按200一组分批
        PS->>RQ: 存入对应渠道的Redis队列
        
        alt 存储成功
            RQ-->>PS: 确认存储成功
            PS-->>C: 返回接收成功信息
        else 存储失败
            RQ-->>PS: 存储失败
            PS-->>C: 返回处理失败信息
        end
        
        loop 定时检查
            TS->>RQ: 检查Redis队列
            RQ-->>TS: 返回队列数据
            
            alt 队列有数据
                TS->>RQ: 从Redis队列出队
                RQ-->>TS: 返回推送任务数据
                
                alt 苹果推送
                    TS->>AP: 调用苹果推送实现
                    AP-->>TS: 返回推送结果
                else 华为推送
                    TS->>HP: 调用华为推送实现
                    HP-->>TS: 返回推送结果
                else 其他渠道推送
                    TS->>OP: 调用对应渠道实现
                    OP-->>TS: 返回推送结果
                end
                
                alt 推送成功
                    TS->>DB: 记录成功结果
                    DB-->>TS: 确认记录成功
                else 推送失败
                    TS->>DB: 记录失败结果
                    DB-->>TS: 确认记录成功
                end
            end
        end
    else token验证失败
        TV-->>PS: 验证失败
        PS-->>C: 返回错误信息
    end
```
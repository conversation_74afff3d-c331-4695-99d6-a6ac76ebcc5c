# Mega Cloud 技术栈规范

## 1. 核心框架

### 1.1 基础框架
- **Spring Boot**: 2.6.3
- **Spring Cloud**: 2020.0.3
- **Java**: 11 运行时
- **Maven**: 构建管理和多模块项目结构

## 2. 数据库与持久化

### 2.1 数据库
- **MySQL**: 5.1.49 作为主数据库
- **Redis**: 缓存和会话管理
- **ClickHouse**: 分析数据存储

### 2.2 ORM框架
- **MyBatis**: 持久层框架
- **TkMapper**: MyBatis 通用 Mapper

## 3. 第三方服务集成

### 3.1 阿里云服务
- **Aliyun OSS**: 文件存储服务
- **Aliyun SDK**: 云服务集成（认证、短信等）

### 3.2 其他第三方服务
- **RongCloud IM**: 即时通讯服务
- **Sentry**: 错误跟踪和监控
- **DingTalk SDK**: 企业集成

## 4. 开发工具

### 4.1 代码生成与处理
- **Lombok**: 代码生成，减少样板代码
- **MyBatis Generator**: 代码生成工具
- **FreeMarker**: 模板处理引擎

### 4.2 API文档
- **Swagger/SpringFox**: 3.0.0 版本，用于 API 文档生成

## 5. 部署与基础设施

### 5.1 容器化
- **Docker**: 容器化部署
- **Harbor Registry**: 172.16.50.231:1180 镜像仓库

### 5.2 监控
- **Spring Boot Admin**: 服务监控
- 自定义 Shell 脚本用于部署自动化

## 6. 关键依赖库

### 6.1 JSON处理
- **FastJSON**: 1.2.83 版本

### 6.2 文档处理
- **Apache POI**: Excel 操作

### 6.3 HTTP客户端
- **OkHttp**: 4.12.0 版本

### 6.4 加密
- **BouncyCastle**: 加密算法库

## 7. 常用构建命令

### 7.1 基础构建命令
```bash
# 编译所有模块
mvn clean compile

# 打包所有模块
mvn clean package

# 运行测试
mvn test
```

### 7.2 代码生成命令
```bash
# 生成 MyBatis 代码
mvn mybatis-generator:generate
```

### 7.3 Docker相关命令
```bash
# 构建 Docker 镜像并推送
mvn clean package dockerfile:build dockerfile:push
```

### 7.4 服务管理命令
```bash
# 启动服务（使用项目脚本）
./mega-cloud.sh start [module] [profile] [port] [tag] [memory] [registry]

# 停止服务
./mega-cloud.sh stop [module] [profile] [port]

# 重启服务
./mega-cloud.sh restart [module] [profile] [port] [tag] [memory] [registry]
```

## 8. 开发环境配置

### 8.1 必需软件
- Java 11 JDK
- Maven 3.6+
- MySQL 5.1.49+
- Redis
- Docker（用于部署）

### 8.2 IDE推荐配置
- IntelliJ IDEA 或 Eclipse
- Lombok 插件
- MyBatis 插件
- Docker 插件

## 9. 版本管理策略

### 9.1 依赖版本
- 所有模块使用统一的父 POM 管理版本
- 第三方依赖版本在父 POM 中集中定义
- 避免在子模块中直接指定版本号

### 9.2 发布版本
- 使用语义化版本控制 (Semantic Versioning)
- 格式：MAJOR.MINOR.PATCH
- 开发版本使用 SNAPSHOT 后缀

## 10. 性能与监控

### 10.1 缓存策略
- Redis 用于会话管理
- 应用级缓存使用 Spring Cache
- 数据库查询结果缓存

### 10.2 监控指标
- 应用性能监控 (APM)
- 数据库连接池监控
- JVM 内存和 GC 监控
- 接口响应时间监控

## 11. 安全配置

### 11.1 认证授权
- JWT Token 认证
- Redis 存储用户会话
- 权限基于角色的访问控制 (RBAC)

### 11.2 数据安全
- 数据库连接加密
- 敏感数据加密存储
- API 接口防重放攻击

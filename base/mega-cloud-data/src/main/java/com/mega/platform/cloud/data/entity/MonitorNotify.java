package com.mega.platform.cloud.data.entity;

import java.util.Date;
import javax.persistence.*;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

@Getter
@Setter
@Accessors(chain = true)
@Table(name = "monitor_notify")
public class MonitorNotify {
    /**
     * 通知配置ID
     */
    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.IDENTITY, generator = "SELECT LAST_INSERT_ID()")
    private Long id;

    /**
     * 关联的告警规则ID
     */
    @Column(name = "monitor_rule_id")
    private Long monitorRuleId;

    /**
     * 通知方式：1=邮件，2=短信，3=飞书，4=微信
     */
    @Column(name = "notify_type")
    private Integer notifyType;

    /**
     * 通知目标，如邮箱地址、手机号或Webhook
     */
    @Column(name = "notify_target")
    private String notifyTarget;

    /**
     * 是否在恢复时也发送通知
     */
    @Column(name = "notify_on_recover")
    private Byte notifyOnRecover;

    /**
     * @谁、逗号分割
     */
    @Column(name = "at_admin_user_ids")
    private String atAdminUserIds;

    @Column(name = "create_time")
    private Date createTime;

    @Column(name = "update_time")
    private Date updateTime;

    @Column(name = "delsign")
    private Byte delsign;
}
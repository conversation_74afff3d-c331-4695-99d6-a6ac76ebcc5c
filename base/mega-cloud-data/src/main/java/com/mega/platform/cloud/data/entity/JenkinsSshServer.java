package com.mega.platform.cloud.data.entity;

import java.util.Date;
import javax.persistence.*;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

@Getter
@Setter
@Accessors(chain = true)
@Table(name = "jenkins_ssh_server")
public class JenkinsSshServer {
    /**
     * 主键
     */
    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.IDENTITY, generator = "SELECT LAST_INSERT_ID()")
    private Long id;

    /**
     * 服务器名称（自定义标识）
     */
    @Column(name = "server_name")
    private String serverName;

    /**
     * Jenkins服务ID
     */
    @Column(name = "jenkins_service_id")
    private Long jenkinsServiceId;

    /**
     * 云服务器ID，关联ecs_server(id)
     */
    @Column(name = "ecs_server_id")
    private Long ecsServerId;

    /**
     * SSH登录用户名（如不填业务默认R）
     */
    @Column(name = "username")
    private String username;

    /**
     * SSH登录密码（如不填业务默认M）
     */
    @Column(name = "password")
    private String password;

    /**
     * 备注
     */
    @Column(name = "remark")
    private String remark;

    @Column(name = "create_time")
    private Date createTime;

    @Column(name = "update_time")
    private Date updateTime;

    /**
     * 逻辑删除标志
     */
    @Column(name = "delsign")
    private Byte delsign;
}
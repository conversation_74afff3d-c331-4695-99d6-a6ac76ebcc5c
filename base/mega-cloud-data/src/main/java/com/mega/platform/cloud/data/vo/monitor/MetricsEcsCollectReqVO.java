package com.mega.platform.cloud.data.vo.monitor;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.mega.platform.cloud.data.dto.monitor.MetricsDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

@Data
@ApiModel
public class MetricsEcsCollectReqVO {
    @ApiModelProperty("指标列表")
    @NotNull
    private List<MetricsDTO> metrics;
    @ApiModelProperty("上报时间")
    @NotNull
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date collectTime;
}

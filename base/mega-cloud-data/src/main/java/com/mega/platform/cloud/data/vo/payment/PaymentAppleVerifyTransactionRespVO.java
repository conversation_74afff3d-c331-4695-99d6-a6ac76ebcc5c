package com.mega.platform.cloud.data.vo.payment;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.persistence.Access;

@ApiModel("苹果交易验证响应")
@Data
@Accessors(chain = true)
public class PaymentAppleVerifyTransactionRespVO {
    @ApiModelProperty(value = "苹果交易id")
    private String transactionId;

    @ApiModelProperty(value = "苹果产品id")
    private String productId;

    @ApiModelProperty(value = "校验结果")
    private boolean success;
}

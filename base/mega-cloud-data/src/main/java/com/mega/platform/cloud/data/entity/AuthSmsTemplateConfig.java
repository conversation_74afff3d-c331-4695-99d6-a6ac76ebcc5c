package com.mega.platform.cloud.data.entity;

import java.util.Date;
import javax.persistence.*;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

@Getter
@Setter
@Accessors(chain = true)
@Table(name = "auth_sms_template_config")
public class AuthSmsTemplateConfig {
    /**
     * 主键ID
     */
    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.IDENTITY, generator = "SELECT LAST_INSERT_ID()")
    private Long id;

    /**
     * 应用ID，关联项目或业务系统
     */
    @Column(name = "project_app_id")
    private Long projectAppId;

    /**
     * 验证码用途类型，例如 login、register、reset_pwd、bind_phone 等
     */
    @Column(name = "type")
    private String type;

    /**
     * 认证渠道ID，关联third_platform表
     */
    @Column(name = "third_platform_id")
    private Long thirdPlatformId;

    /**
     * 第三方短信模板code
     */
    @Column(name = "sms_template_code")
    private String smsTemplateCode;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Date updateTime;

    /**
     * 逻辑删除标识，0=正常，1=删除
     */
    @Column(name = "delsign")
    private Byte delsign;
}
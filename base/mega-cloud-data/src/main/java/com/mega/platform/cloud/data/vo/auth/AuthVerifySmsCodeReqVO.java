package com.mega.platform.cloud.data.vo.auth;

import com.mega.platform.cloud.data.vo.BaseReqVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;
@Data
public class AuthVerifySmsCodeReqVO extends BaseReqVO {
    @ApiModelProperty(value = "区号（如 +86）", example = "+86", required = true)
    @NotBlank(message = "区号不能为空")
    private String areaCode;

    @ApiModelProperty(value = "手机号", example = "13800138000", required = true)
    @NotBlank(message = "手机号不能为空")
    @Pattern(regexp = "^1\\d{10}$", message = "手机号格式不正确")
    private String phoneNum;

    @NotBlank(message = "验证码不能为空")
    private String code;

    @NotBlank(message = "clientIp不能为空")
    private String clientIp;

    @NotBlank(message = "deviceId不能为空")
    private String deviceId;
}

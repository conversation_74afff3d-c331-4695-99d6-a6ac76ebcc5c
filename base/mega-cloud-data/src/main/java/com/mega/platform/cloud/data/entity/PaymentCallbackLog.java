package com.mega.platform.cloud.data.entity;

import java.util.Date;
import javax.persistence.*;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

@Getter
@Setter
@Accessors(chain = true)
@Table(name = "payment_callback_log")
public class PaymentCallbackLog {
    /**
     * 主键ID
     */
    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.IDENTITY, generator = "SELECT LAST_INSERT_ID()")
    private Long id;

    /**
     * 关联的支付订单ID
     */
    @Column(name = "payment_order_id")
    private Long paymentOrderId;

    /**
     * 支付平台编码，如 wechat, alipay, apple 等
     */
    @Column(name = "platform_code")
    private String platformCode;

    /**
     * 应用ID
     */
    @Column(name = "project_app_id")
    private Long projectAppId;

    /**
     * 支付平台订单号，比如alipay
     */
    @Column(name = "order_no")
    private String orderNo;

    /**
     * 外显应用订单号（中台算法随机生成）
     */
    @Column(name = "out_order_no")
    private String outOrderNo;

    /**
     * 解析后的字段结构（可选）
     */
    @Column(name = "parsed_data")
    private String parsedData;

    /**
     * 处理状态（RECEIVED、SUCCESS、FAILED 等）
     */
    @Column(name = "status")
    private String status;

    /**
     * 错误信息（如果处理失败）
     */
    @Column(name = "error_message")
    private String errorMessage;

    /**
     * 回调收到时间（可提取）
     */
    @Column(name = "notify_time")
    private Date notifyTime;

    /**
     * 系统处理时间
     */
    @Column(name = "process_time")
    private Date processTime;

    @Column(name = "create_time")
    private Date createTime;

    @Column(name = "update_time")
    private Date updateTime;

    /**
     * 逻辑删除标志：0正常，1删除
     */
    @Column(name = "delsign")
    private Byte delsign;

    /**
     * 原始回调内容（JSON或XML等）
     */
    @Column(name = "raw_content")
    private String rawContent;
}
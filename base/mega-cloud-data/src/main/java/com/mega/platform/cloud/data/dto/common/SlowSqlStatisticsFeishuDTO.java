package com.mega.platform.cloud.data.dto.common;

import com.mega.platform.cloud.data.annotation.FeishuParam;
import lombok.Data;

/**
 * 慢查询统计钉钉bean
 */
@Data
public class SlowSqlStatisticsFeishuDTO extends BaseFeishuDTO{
    public SlowSqlStatisticsFeishuDTO(String method, String slowsql, Long cost) {
        super.setTitle("慢查询统计");
        this.method = method;
        this.slowsql = slowsql;
        this.costStr = cost + "ms";
    }

    @FeishuParam("慢查询方法")
    private String method;//慢查询方法名
    @FeishuParam("慢查询sql")
    private String slowsql;//慢查询sql
    @FeishuParam("慢查询时间")
    private String costStr;//慢查询毫秒数

}

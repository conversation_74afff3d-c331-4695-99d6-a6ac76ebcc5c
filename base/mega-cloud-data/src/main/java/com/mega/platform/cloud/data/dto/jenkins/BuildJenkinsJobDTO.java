package com.mega.platform.cloud.data.dto.jenkins;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Map;

@Data
@Accessors(chain = true)
public class BuildJenkinsJobDTO {
    private String jobName;
    private Map<String, String> paramMap;

    public BuildJenkinsJobDTO(String jobName, Map<String, String> paramMap) {
        this.jobName = jobName;
        this.paramMap = paramMap;
    }
}

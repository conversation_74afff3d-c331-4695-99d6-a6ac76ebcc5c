package com.mega.platform.cloud.data.entity;

import java.math.BigDecimal;
import java.util.Date;
import javax.persistence.*;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

@Getter
@Setter
@Accessors(chain = true)
@Table(name = "monitor_rule")
public class MonitorRule {
    /**
     * 告警规则ID
     */
    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.IDENTITY, generator = "SELECT LAST_INSERT_ID()")
    private Long id;

    /**
     * 告警名称
     */
    @Column(name = "name")
    private String name;

    /**
     * 监控指标类型ID
     */
    @Column(name = "monitor_metrics_id")
    private Long monitorMetricsId;

    /**
     * 比较符ID，如 > < >= <= ==
     */
    @Column(name = "threshold_operator")
    private Integer thresholdOperator;

    /**
     * 阈值
     */
    @Column(name = "threshold_value")
    private BigDecimal thresholdValue;

    /**
     * 扫描间隔时间
     */
    @Column(name = "scan_interval_second")
    private Integer scanIntervalSecond;

    /**
     * 报警持续时间（秒）
     */
    @Column(name = "duration_second")
    private Integer durationSecond;

    /**
     * 持续时间的触发比率
     */
    @Column(name = "duration_ratio")
    private BigDecimal durationRatio;

    /**
     * 采集数据ID
     */
    @Column(name = "source_id")
    private Long sourceId;

    /**
     * 类型 
     */
    @Column(name = "source_type")
    private Integer sourceType;

    /**
     * 上次检查时间
     */
    @Column(name = "last_checked_time")
    private Date lastCheckedTime;

    /**
     * 报警间隔时长
     */
    @Column(name = "alarm_interval_second")
    private Integer alarmIntervalSecond;

    /**
     * 是否启用
     */
    @Column(name = "is_enabled")
    private Byte isEnabled;

    /**
     * 告警级别：1=低，2=中，3=高
     */
    @Column(name = "level")
    private Integer level;

    /**
     * 说明
     */
    @Column(name = "remark")
    private String remark;

    /**
     * 指标状态
     */
    @Column(name = "status")
    private Integer status;

    @Column(name = "create_time")
    private Date createTime;

    @Column(name = "update_time")
    private Date updateTime;

    @Column(name = "delsign")
    private Byte delsign;
}
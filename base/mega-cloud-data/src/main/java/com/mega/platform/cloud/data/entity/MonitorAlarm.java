package com.mega.platform.cloud.data.entity;

import java.math.BigDecimal;
import java.util.Date;
import javax.persistence.*;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

@Getter
@Setter
@Accessors(chain = true)
@Table(name = "monitor_alarm")
public class MonitorAlarm {
    /**
     * 告警事件ID
     */
    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.IDENTITY, generator = "SELECT LAST_INSERT_ID()")
    private Long id;

    /**
     * 触发的规则ID
     */
    @Column(name = "monitor_rule_id")
    private Long monitorRuleId;

    /**
     * 指标类型ID
     */
    @Column(name = "monitor_metric_id")
    private Long monitorMetricId;

    /**
     * 实际触发值
     */
    @Column(name = "actual_value")
    private BigDecimal actualValue;

    @Column(name = "trigger_ratio")
    private BigDecimal triggerRatio;

    /**
     * 状态：1待处理，2处理中，3已恢复
     */
    @Column(name = "status")
    private Integer status;

    /**
     * 触发时间
     */
    @Column(name = "trigger_time")
    private Date triggerTime;

    /**
     * 恢复时间（可为空）
     */
    @Column(name = "recover_time")
    private Date recoverTime;

    /**
     * 最后一次报警时间
     */
    @Column(name = "last_alarm_time")
    private Date lastAlarmTime;

    /**
     * 报警次数
     */
    @Column(name = "alarm_num")
    private Integer alarmNum;

    /**
     * 备注
     */
    @Column(name = "remark")
    private String remark;

    @Column(name = "create_time")
    private Date createTime;

    @Column(name = "update_time")
    private Date updateTime;

    @Column(name = "delsign")
    private Byte delsign;
}
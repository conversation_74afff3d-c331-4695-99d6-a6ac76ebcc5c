package com.mega.platform.cloud.data.entity;

import java.util.Date;
import javax.persistence.*;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

@Getter
@Setter
@Accessors(chain = true)
@Table(name = "project_app_permission")
public class ProjectAppPermission {
    /**
     * 主键ID
     */
    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.IDENTITY, generator = "SELECT LAST_INSERT_ID()")
    private Long id;

    /**
     * 所属App的ID，关联project_app表
     */
    @Column(name = "project_app_id")
    private Long projectAppId;

    /**
     * Ant风格路径匹配，例如 /api/v1/user/**
     */
    @Column(name = "url_pattern")
    private String urlPattern;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 修改时间
     */
    @Column(name = "update_time")
    private Date updateTime;

    /**
     * 是否删除：0=未删除，1=已删除
     */
    @Column(name = "delsign")
    private Boolean delsign;
}
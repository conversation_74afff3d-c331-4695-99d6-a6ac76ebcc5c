package com.mega.platform.cloud.data.vo.payment;

import com.mega.platform.cloud.data.vo.BaseReqVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
@Data
@ApiModel("苹果交易回调请求参数")
public class PaymentAppleCallbackReqVO extends BaseReqVO {
    @ApiModelProperty(value = "苹果回调signedPayload", required = true)
    @NotBlank(message = "苹果回调signedPayload不能为空")
    private String signedPayload;
}

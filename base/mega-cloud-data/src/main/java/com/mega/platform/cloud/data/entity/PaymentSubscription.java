package com.mega.platform.cloud.data.entity;

import java.util.Date;
import javax.persistence.*;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

@Getter
@Setter
@Accessors(chain = true)
@Table(name = "payment_subscription")
public class PaymentSubscription {
    /**
     * 主键ID
     */
    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.IDENTITY, generator = "SELECT LAST_INSERT_ID()")
    private Long id;

    /**
     * 商品配置ID
     */
    @Column(name = "payment_product_config_id")
    private Long paymentProductConfigId;

    /**
     * 初始订阅订单ID
     */
    @Column(name = "payment_order_id")
    private Long paymentOrderId;

    /**
     * 当前订单ID
     */
    @Column(name = "cur_payment_order_id")
    private Long curPaymentOrderId;

    /**
     * 订阅状态：1=ACTIVE(活跃), 2=INACTIVE(不活跃), 3=EXPIRED(已过期), 4=CANCELLED(已取消)
     */
    @Column(name = "status")
    private Byte status;

    /**
     * 下次扣款时间
     */
    @Column(name = "next_charge_time")
    private Date nextChargeTime;

    /**
     * 订阅开始时间
     */
    @Column(name = "start_time")
    private Date startTime;

    /**
     * 订阅结束时间
     */
    @Column(name = "end_time")
    private Date endTime;

    /**
     * 是否自动续订
     */
    @Column(name = "is_auto_renew")
    private Boolean isAutoRenew;

    /**
     * 取消原因
     */
    @Column(name = "cancel_reason")
    private String cancelReason;

    /**
     * 最后更新时间
     */
    @Column(name = "last_update_time")
    private Date lastUpdateTime;

    @Column(name = "create_time")
    private Date createTime;

    @Column(name = "update_time")
    private Date updateTime;

    /**
     * 逻辑删除标志
     */
    @Column(name = "delsign")
    private Byte delsign;
}
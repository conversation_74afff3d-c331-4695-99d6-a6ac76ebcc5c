package com.mega.platform.cloud.data.entity;

import java.util.Date;
import javax.persistence.*;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

@Getter
@Setter
@Accessors(chain = true)
@Table(name = "admin_user_role_binding")
public class AdminUserRoleBinding {
    /**
     * 绑定ID
     */
    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.IDENTITY, generator = "SELECT LAST_INSERT_ID()")
    private Long id;

    /**
     * 管理员ID
     */
    @Column(name = "admin_user_id")
    private Long adminUserId;

    /**
     * 角色ID
     */
    @Column(name = "admin_role_id")
    private Long adminRoleId;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    @Column(name = "delsign")
    private Boolean delsign;
}
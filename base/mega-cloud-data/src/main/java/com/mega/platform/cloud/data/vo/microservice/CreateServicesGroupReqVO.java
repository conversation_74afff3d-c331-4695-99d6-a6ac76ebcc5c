package com.mega.platform.cloud.data.vo.microservice;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.Map;

@Data
@ApiModel
public class CreateServicesGroupReqVO {

    @ApiModelProperty("模板参数值")
    private Map<String, String> jenkinsParams;
    @NotNull
    @ApiModelProperty("管理员id")
    private Long adminUserId;
    @NotNull
    @ApiModelProperty("模板id")
    private Long jenkinsTemplateId;
    @NotNull
    @ApiModelProperty("jenkins实例id")
    private Long jenkinsServiceId;
    @NotNull
    @ApiModelProperty("项目id")
    private Long projectId;
    @NotNull
    @ApiModelProperty("appid")
    private Long projectAppId;
    @NotNull
    @ApiModelProperty("服务组名")
    private String servicesGroupName;
    @NotNull
    @ApiModelProperty("启动方式")
    private Integer serviceUpdateId;
    @NotNull
    @ApiModelProperty("环境")
    private String serviceEnv;
    @ApiModelProperty("保活数量")
    private Integer serviceAliveNum = 0;
    @ApiModelProperty("是否使用consul")
    private Boolean checkConsul = false;
}

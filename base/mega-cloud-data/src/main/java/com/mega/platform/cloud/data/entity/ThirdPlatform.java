package com.mega.platform.cloud.data.entity;

import java.util.Date;
import javax.persistence.*;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

@Getter
@Setter
@Accessors(chain = true)
@Table(name = "third_platform")
public class ThirdPlatform {
    /**
     * 主键ID
     */
    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.IDENTITY, generator = "SELECT LAST_INSERT_ID()")
    private Long id;

    /**
     * 平台标识（如 wechat、apple、google、alipay、fcm）
     */
    @Column(name = "platform_code")
    private String platformCode;

    /**
     * 平台名称，用于展示（如 微信、苹果、谷歌、支付宝、FCM）
     */
    @Column(name = "platform_name")
    private String platformName;

    /**
     * 备注说明，例如用途或配置说明
     */
    @Column(name = "description")
    private String description;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Date updateTime;

    /**
     * 状态：0-启用，1-禁用
     */
    @Column(name = "delsign")
    private Byte delsign;
}
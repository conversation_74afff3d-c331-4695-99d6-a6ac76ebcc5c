package com.mega.platform.cloud.data.entity;

import java.util.Date;
import javax.persistence.*;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

@Getter
@Setter
@Accessors(chain = true)
@Table(name = "jenkins_job_template_param_value")
public class JenkinsJobTemplateParamValue {
    /**
     * 主键
     */
    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.IDENTITY, generator = "SELECT LAST_INSERT_ID()")
    private Long id;

    /**
     * 参数ID，关联jenkins_job_template_param(id)
     */
    @Column(name = "jenkins_job_templete_param_id")
    private Long jenkinsJobTempleteParamId;

    /**
     * 表id
     */
    @Column(name = "services_data_id")
    private Long servicesDataId;

    /**
     * 参数类型 1-group上的 2-service上的
     */
    @Column(name = "services_data_type")
    private Integer servicesDataType;

    /**
     * 实际传递的参数值
     */
    @Column(name = "param_value")
    private String paramValue;

    /**
     * 备注
     */
    @Column(name = "remark")
    private String remark;

    @Column(name = "create_time")
    private Date createTime;

    @Column(name = "update_time")
    private Date updateTime;

    /**
     * 逻辑删除标志
     */
    @Column(name = "delsign")
    private Byte delsign;
}
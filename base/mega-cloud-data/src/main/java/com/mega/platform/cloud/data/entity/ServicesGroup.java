package com.mega.platform.cloud.data.entity;

import java.util.Date;
import javax.persistence.*;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

@Getter
@Setter
@Accessors(chain = true)
@Table(name = "services_group")
public class ServicesGroup {
    /**
     * 微服务组id
     */
    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.IDENTITY, generator = "SELECT LAST_INSERT_ID()")
    private Long id;

    /**
     * 服务组名:mysql-prod,kafka-test,consul,push-cloud-push-api-test,vpn-cloud-router-prod
     */
    @Column(name = "name")
    private String name;

    /**
     * 产品ID
     */
    @Column(name = "project_id")
    private Long projectId;

    /**
     * appId
     */
    @Column(name = "project_app_id")
    private Long projectAppId;

    /**
     * 微服务更新方式 2004
     */
    @Column(name = "services_update_type")
    private Integer servicesUpdateType;

    /**
     * 微服务环境 2005
     */
    @Column(name = "services_env")
    private String servicesEnv;

    /**
     * 微服务日志格式id
     */
    @Column(name = "services_log_format_id")
    private Long servicesLogFormatId;

    /**
     * 如果是滚服/导流重启 配置需要保活的数量
     */
    @Column(name = "services_alive_num")
    private Integer servicesAliveNum;

    /**
     * jenkins_services表id
     */
    @Column(name = "jenkins_services_id")
    private Long jenkinsServicesId;

    /**
     * 关联jenkins_job_template(id)
     */
    @Column(name = "jenkins_template_id")
    private Long jenkinsTemplateId;

    /**
     * 1自研 3三方
     */
    @Column(name = "is_self")
    private Integer isSelf;

    /**
     * 负责人
     */
    @Column(name = "admin_user_id")
    private Long adminUserId;

    /**
     * 备注
     */
    @Column(name = "remark")
    private String remark;

    /**
     * 状态 2001
     */
    @Column(name = "status")
    private Integer status;

    /**
     * 运行状态 2003
     */
    @Column(name = "running_status")
    private Integer runningStatus;

    /**
     * 是否检查consul 0-不 1-检查
     */
    @Column(name = "check_consul")
    private Byte checkConsul;

    @Column(name = "create_time")
    private Date createTime;

    @Column(name = "update_time")
    private Date updateTime;

    /**
     * 是否删除：0未删除，1已删除
     */
    @Column(name = "delsign")
    private Byte delsign;
}
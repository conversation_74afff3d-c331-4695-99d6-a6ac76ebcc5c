package com.mega.platform.cloud.data.dto.jenkins;

import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class CreateJenkinsUserTokenDTO extends JenkinsGroovyDTO{
    private final static String groovyScriptFormat = "import jenkins.model.*\n" +
            "import hudson.security.*\n" +
            "import jenkins.security.ApiTokenProperty\n" + "\n" +
            "def user = User.getById(\"%s\", false) // 填用户名\n" +
            "def apiTokenProperty = user.getProperty(ApiTokenProperty.class)\n" +
            "def tokenStore = apiTokenProperty.getTokenStore()\n" +
            "def tokenName = \"generated-by-script\"\n" +
            "def result = tokenStore.generateNewToken(tokenName)\n" +
            "user.save()\n" +
            "println(\"${result.plainValue}\")";

    private String userName;

    public CreateJenkinsUserTokenDTO(String userName) {
        this.userName = userName;
    }

    @Override
    public String getGroovyScriptStr() {
        return String.format(groovyScriptFormat, userName);
    }
}

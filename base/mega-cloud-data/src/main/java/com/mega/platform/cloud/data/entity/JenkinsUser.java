package com.mega.platform.cloud.data.entity;

import java.util.Date;
import javax.persistence.*;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

@Getter
@Setter
@Accessors(chain = true)
@Table(name = "jenkins_user")
public class JenkinsUser {
    /**
     * 主键
     */
    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.IDENTITY, generator = "SELECT LAST_INSERT_ID()")
    private Long id;

    /**
     * jenkins服务id，关联jenkins_service(id)
     */
    @Column(name = "jenkins_services_id")
    private Long jenkinsServicesId;

    /**
     * 所属用户ID，关联admin_user(id)
     */
    @Column(name = "admin_user_id")
    private Long adminUserId;

    /**
     * Jenkins 用户名
     */
    @Column(name = "jenkins_username")
    private String jenkinsUsername;

    /**
     * Jenkins API Token
     */
    @Column(name = "api_token")
    private String apiToken;

    /**
     * 备注
     */
    @Column(name = "remark")
    private String remark;

    @Column(name = "create_time")
    private Date createTime;

    @Column(name = "update_time")
    private Date updateTime;

    /**
     * 逻辑删除标志
     */
    @Column(name = "delsign")
    private Byte delsign;
}
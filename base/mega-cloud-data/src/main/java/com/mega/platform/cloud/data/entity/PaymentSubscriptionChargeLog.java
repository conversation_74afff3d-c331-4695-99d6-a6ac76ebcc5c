package com.mega.platform.cloud.data.entity;

import java.util.Date;
import javax.persistence.*;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

@Getter
@Setter
@Accessors(chain = true)
@Table(name = "payment_subscription_charge_log")
public class PaymentSubscriptionChargeLog {
    /**
     * 主键ID
     */
    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.IDENTITY, generator = "SELECT LAST_INSERT_ID()")
    private Long id;

    /**
     * 计划扣款时间
     */
    @Column(name = "expected_charge_time")
    private Date expectedChargeTime;

    /**
     * 实际扣款时间
     */
    @Column(name = "actual_charge_time")
    private Date actualChargeTime;

    /**
     * 扣款状态
     */
    @Column(name = "status")
    private String status;

    /**
     * 错误码
     */
    @Column(name = "error_code")
    private String errorCode;

    /**
     * 错误信息
     */
    @Column(name = "error_message")
    private String errorMessage;

    /**
     * 关联订单ID
     */
    @Column(name = "payment_order_id")
    private Long paymentOrderId;

    /**
     * 订阅ID
     */
    @Column(name = "payment_subscrition_id")
    private Long paymentSubscritionId;

    @Column(name = "create_time")
    private Date createTime;

    @Column(name = "update_time")
    private Date updateTime;

    /**
     * 逻辑删除标志
     */
    @Column(name = "delsign")
    private Byte delsign;
}
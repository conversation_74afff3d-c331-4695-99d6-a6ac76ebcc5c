package com.mega.platform.cloud.data.entity;

import java.util.Date;
import javax.persistence.*;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

@Getter
@Setter
@Accessors(chain = true)
@Table(name = "admin_user_operate_log")
public class AdminUserOperateLog {
    /**
     * 日志ID
     */
    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.IDENTITY, generator = "SELECT LAST_INSERT_ID()")
    private Long id;

    /**
     * 管理员ID
     */
    @Column(name = "admin_user_id")
    private Long adminUserId;

    /**
     * API路径
     */
    @Column(name = "api_path")
    private String apiPath;

    /**
     * 操作类型描述
     */
    @Column(name = "action")
    private String action;

    /**
     * 目标表名
     */
    @Column(name = "target_table")
    private String targetTable;

    /**
     * 目标记录ID
     */
    @Column(name = "target_id")
    private String targetId;

    /**
     * IP地址
     */
    @Column(name = "ip")
    private String ip;

    /**
     * 设备信息
     */
    @Column(name = "device")
    private String device;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 详细信息
     */
    @Column(name = "detail")
    private String detail;
}
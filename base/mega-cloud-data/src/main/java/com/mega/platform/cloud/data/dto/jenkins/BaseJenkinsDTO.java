package com.mega.platform.cloud.data.dto.jenkins;

import com.mega.platform.cloud.data.entity.JenkinsServices;
import com.mega.platform.cloud.data.entity.JenkinsUser;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class BaseJenkinsDTO {
    private String jenkinsUrl;
    private String jenkinsUserName;
    private String jenkinsUserToken;
    private Long adminUserId;

    public BaseJenkinsDTO(String jenkinsUrl, String jenkinsUserName, String jenkinsUserToken) {
        this.jenkinsUrl = jenkinsUrl;
        this.jenkinsUserName = jenkinsUserName;
        this.jenkinsUserToken = jenkinsUserToken;
    }

    public BaseJenkinsDTO(JenkinsServices jenkinsServices, JenkinsUser jenkinsUser) {
        this.jenkinsUrl = jenkinsServices.getJenkinsUrl();
        this.jenkinsUserName = jenkinsUser.getJenkinsUsername();
        this.jenkinsUserToken = jenkinsUser.getApiToken();
        this.adminUserId = jenkinsUser.getAdminUserId();
    }
}

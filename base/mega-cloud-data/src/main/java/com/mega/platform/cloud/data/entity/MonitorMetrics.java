package com.mega.platform.cloud.data.entity;

import java.util.Date;
import javax.persistence.*;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

@Getter
@Setter
@Accessors(chain = true)
@Table(name = "monitor_metrics")
public class MonitorMetrics {
    /**
     * 指标类型ID
     */
    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.IDENTITY, generator = "SELECT LAST_INSERT_ID()")
    private Long id;

    /**
     * 指标名称（如CPU使用率）
     */
    @Column(name = "name")
    private String name;

    /**
     * 唯一key（如 cpu_usage）
     */
    @Column(name = "key")
    private String key;

    /**
     * 单位（如%, MB）
     */
    @Column(name = "unit")
    private String unit;

    /**
     * 指标类型 
     */
    @Column(name = "type")
    private Integer type;

    /**
     * 描述说明
     */
    @Column(name = "remark")
    private String remark;

    @Column(name = "create_time")
    private Date createTime;

    @Column(name = "update_time")
    private Date updateTime;

    /**
     * 是否删除：0未删除，1已删除
     */
    @Column(name = "delsign")
    private Byte delsign;
}
package com.mega.platform.cloud.data.dto.common;

import com.mega.platform.cloud.data.annotation.FeishuParam;
import lombok.Data;

import java.text.SimpleDateFormat;
import java.util.Date;

@Data
public class MonitorAlarmNotifyMailDTO extends BaseFeishuDTO {
    public MonitorAlarmNotifyMailDTO(String metricsName, String ruleName, Date firstAlarmTime, Integer alarmNum, Integer durationSeconds) {
        this.metricsName = metricsName;
        this.ruleName = ruleName;
        this.firstAlarmTimeStr = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(firstAlarmTime);
        this.alarmNumStr = "第" + alarmNum + "次";
        this.durationSecondsStr = durationSeconds + "秒";
        super.setTitle("服务监控报警");
    }

    @FeishuParam("监控指标")
    private String metricsName;
    @FeishuParam("报警规则")
    private String ruleName;
    @FeishuParam("首次报警时间")
    private String firstAlarmTimeStr;
    @FeishuParam("报警次数")
    private String alarmNumStr;
    @FeishuParam("持续时间(s)")
    private String durationSecondsStr;
}

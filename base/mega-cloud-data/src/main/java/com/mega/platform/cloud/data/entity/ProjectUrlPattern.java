package com.mega.platform.cloud.data.entity;

import lombok.Data;
import lombok.experimental.Accessors;

import javax.persistence.*;
import java.time.LocalDateTime;

/**
 * app路由配置表
 */
@Data
@Accessors(chain = true)
@Entity
@Table(name = "project_url_pattern")
public class ProjectUrlPattern {
    
    /**
     * AppID
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Integer id;
    
    /**
     * 路由名称 例：auth路由、access路由
     */
    @Column(name = "name")
    private String name;
    
    /**
     * Ant风格路径匹配，例如 /api/v1/user/**
     */
    @Column(name = "url_pattern")
    private String urlPattern;
    
    /**
     * 描述
     */
    @Column(name = "remark")
    private String remark;
    
    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private LocalDateTime createTime;
    
    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private LocalDateTime updateTime;
    
    /**
     * 是否删除：0未删除，1已删除
     */
    @Column(name = "delsign")
    private Integer delsign;
}
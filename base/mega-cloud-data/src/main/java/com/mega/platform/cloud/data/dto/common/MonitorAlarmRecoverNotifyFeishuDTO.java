package com.mega.platform.cloud.data.dto.common;

import com.mega.platform.cloud.data.annotation.FeishuParam;
import com.mega.platform.cloud.data.dto.monitor.MetricsAlarmDTO;
import com.mega.platform.cloud.data.dto.monitor.MetricsNotifyDTO;
import lombok.Data;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;

@Data
public class MonitorAlarmRecoverNotifyFeishuDTO extends BaseFeishuDTO {
    public MonitorAlarmRecoverNotifyFeishuDTO(MetricsAlarmDTO alarmDTO, MetricsNotifyDTO notifyDTO) {
        super.setTitle("服务监控恢复正常");
        this.alarmId = alarmDTO.getId();
        if (notifyDTO.getSourceType() == 1) {
            this.ecsName = notifyDTO.getEcsName();
        }
        if (notifyDTO.getSourceType() == 2) {
            this.servicesName = notifyDTO.getServicesName();
            this.servicesGroupName = notifyDTO.getServicesGroupName();
        }
        this.projectName = notifyDTO.getProjectName();
        this.metricsName = notifyDTO.getMetricsName();
        this.ruleName = notifyDTO.getRuleName();
        this.triggerTimeStr = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(alarmDTO.getTriggerTime());
        this.durationSecondsStr = (System.currentTimeMillis() - alarmDTO.getTriggerTime().getTime()) / 1000 + "秒";
        this.currentValue = alarmDTO.getActualValue().toString();
        this.currentRatioStr = alarmDTO.getCurrentRatio().multiply(new BigDecimal(100)) + "%";
        super.addBotUrl(notifyDTO.getNotifyTarget());
        super.setEmoJi(":DONE:");
    }

    @FeishuParam("报警ID")
    private Long alarmId;
    @FeishuParam("项目名")
    private String projectName;
    @FeishuParam("服务组名")
    private String servicesGroupName;
    @FeishuParam("服务名")
    private String servicesName;
    @FeishuParam("机器名")
    private String ecsName;
    @FeishuParam("监控指标")
    private String metricsName;
    @FeishuParam("报警规则")
    private String ruleName;
    @FeishuParam("触发时间")
    private String triggerTimeStr;
    @FeishuParam("当前值")
    private String currentValue;
    @FeishuParam("当前占比")
    private String currentRatioStr;
    @FeishuParam("持续时间(s)")
    private String durationSecondsStr;
}

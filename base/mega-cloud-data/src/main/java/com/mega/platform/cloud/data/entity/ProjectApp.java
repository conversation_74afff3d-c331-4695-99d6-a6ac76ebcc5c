package com.mega.platform.cloud.data.entity;

import java.util.Date;
import javax.persistence.*;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

@Getter
@Setter
@Accessors(chain = true)
@Table(name = "project_app")
public class ProjectApp {
    /**
     * AppID
     */
    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.IDENTITY, generator = "SELECT LAST_INSERT_ID()")
    private Long id;

    @Column(name = "app_key")
    private String appKey;

    /**
     * App秘钥
     */
    @Column(name = "app_secret")
    private String appSecret;

    /**
     * app名称 例：快鸟充值、快鸟推送
     */
    @Column(name = "name")
    private String name;

    /**
     * 项目ID
     */
    @Column(name = "project_id")
    private Long projectId;

    /**
     * 状态 0：不可用 1：正常 2：挂起 3：审核中
     */
    @Column(name = "status")
    private Integer status;

    /**
     * 描述
     */
    @Column(name = "remark")
    private String remark;

    @Column(name = "create_time")
    private Date createTime;

    @Column(name = "update_time")
    private Date updateTime;

    /**
     * 是否删除：0未删除，1已删除
     */
    @Column(name = "delsign")
    private Byte delsign;
}
package com.mega.platform.cloud.data.entity;

import java.util.Date;
import javax.persistence.*;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

@Getter
@Setter
@Accessors(chain = true)
@Table(name = "admin_router")
public class AdminRouter {
    /**
     * 路由ID
     */
    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.IDENTITY, generator = "SELECT LAST_INSERT_ID()")
    private Long id;

    /**
     * 后端路由路径，空表示没有
     */
    @Column(name = "backend_path")
    private String backendPath;

    /**
     * 前端路由路径，空表示没有
     */
    @Column(name = "frontend_path")
    private String frontendPath;

    /**
     * 前端路由名称，空表示没有
     */
    @Column(name = "frontend_name")
    private String frontendName;

    /**
     * 路由描述
     */
    @Column(name = "description")
    private String description;

    /**
     * 父路由ID，0表示根路由
     */
    @Column(name = "parent_admin_router_id")
    private Long parentAdminRouterId;

    @Column(name = "create_time")
    private Date createTime;

    @Column(name = "update_time")
    private Date updateTime;

    /**
     * 路由类别,0 public  1 system  2project 
     */
    @Column(name = "router_cate")
    private Byte routerCate;

    /**
     * 删除标识: 0=未删除, 1=已删除
     */
    @Column(name = "delsign")
    private Boolean delsign;
}
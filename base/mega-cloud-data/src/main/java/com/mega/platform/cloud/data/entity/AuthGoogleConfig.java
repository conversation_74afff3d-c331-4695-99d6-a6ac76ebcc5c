package com.mega.platform.cloud.data.entity;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel("Google 平台配置")
public class AuthGoogleConfig {
    @ApiModelProperty("Google 客户端 ID")
    @JsonProperty("clientId")
    private String clientId;

    @ApiModelProperty("Google iOS 客户端 ID")
    @JsonProperty("iosClientId")
    private String iosClientId;

    @ApiModelProperty("Google Android 客户端 ID")
    @JsonProperty("androidClientId")
    private String androidClientId;
}

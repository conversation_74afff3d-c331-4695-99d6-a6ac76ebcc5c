package com.mega.platform.cloud.data.dto.monitor;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

@Data
@Accessors(chain = true)
@ApiModel
public class MetricsDTO {
    @ApiModelProperty("指标id")
    @NotNull
    private Long metricsId;
    @ApiModelProperty("指标值")
    @NotNull
    private BigDecimal value;
    @ApiModelProperty("指标key")
    @NotNull
    private String key;
}

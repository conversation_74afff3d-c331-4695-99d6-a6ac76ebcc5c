package com.mega.platform.cloud.common.scheduler;

import com.mega.platform.cloud.common.cache.AppPermissionCache;
import com.mega.platform.cloud.common.mapper.ProjectAppPermissionMapper;
import com.mega.platform.cloud.data.entity.ProjectAppPermission;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@Slf4j
public class ConfigCacheRefreshScheduler {
    private final ProjectAppPermissionMapper projectAppPermissionMapper;
    private final AppPermissionCache permissionCache;

    @Autowired
    public ConfigCacheRefreshScheduler(ProjectAppPermissionMapper projectAppPermissionMapper, AppPermissionCache permissionCache) {
        this.projectAppPermissionMapper = projectAppPermissionMapper;
        this.permissionCache = permissionCache;
    }

    @Scheduled(fixedRate = 5 * 60 * 1000) // 每5分钟刷新一次
    public void refreshPermissions() {
        List<ProjectAppPermission> allPermissions = projectAppPermissionMapper.select(new ProjectAppPermission().setDelsign(false));

        Map<Long, List<ProjectAppPermission>> grouped = allPermissions.stream().collect(Collectors.groupingBy(ProjectAppPermission::getProjectAppId));

        permissionCache.replaceAll(grouped);

        log.info("[AppPermissionRefresher] 权限缓存已刷新，共加载 appId 数: " + grouped.size());
    }
}

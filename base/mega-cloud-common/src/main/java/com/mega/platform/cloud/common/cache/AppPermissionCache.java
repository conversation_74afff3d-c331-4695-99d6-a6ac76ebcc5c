package com.mega.platform.cloud.common.cache;

import com.mega.platform.cloud.data.entity.ProjectAppPermission;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
@Component
public class AppPermissionCache {
    private volatile Map<Long, List<ProjectAppPermission>> permissionMap = new ConcurrentHashMap<>();

    public void updatePermissions(Long appId, List<ProjectAppPermission> permissions) {
        permissionMap.put(appId, permissions);
    }

    public List<ProjectAppPermission> getPermissions(Long appId) {
        return permissionMap.getOrDefault(appId, Collections.<ProjectAppPermission>emptyList());
    }

    public void clear() {
        permissionMap.clear();
    }

    public void replaceAll(Map<Long, List<ProjectAppPermission>> newMap) {
        permissionMap = new ConcurrentHashMap<>(newMap);
    }
}

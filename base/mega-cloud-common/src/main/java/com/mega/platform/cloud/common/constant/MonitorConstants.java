package com.mega.platform.cloud.common.constant;

public class MonitorConstants {
    // 指标源类型 1-服务器 2-服务
    public static final int METRICS_SOURCE_TYPE_ECS = 1;
    public static final int METRICS_SOURCE_TYPE_SERVICES = 2;
    // 指标类型 1-服务器指标 2-业务指标
    public static final int METRICS_TYPE_SERVER = 1;
    public static final int METRICS_TYPE_BIZ = 2;

    // 运算符
    public static final int METRICS_THRESHOLD_OPERATOR_GT = 1;
    public static final int METRICS_THRESHOLD_OPERATOR_LT = 2;
    public static final int METRICS_THRESHOLD_OPERATOR_GE = 3;
    public static final int METRICS_THRESHOLD_OPERATOR_LE = 4;
    public static final int METRICS_THRESHOLD_OPERATOR_EQ = 5;

    // 规则状态
    public static final int MONITOR_RULE_STATUS_WARNING = 2;
    public static final int MONITOR_RULE_STATUS_NORMAL = 1;

    // 报警状态
    public static final int MONITOR_ALARM_STATUS_NO_DEAL = 1;
    public static final int MONITOR_ALARM_STATUS_DEALING = 2;
    public static final int MONITOR_ALARM_STATUS_RECOVERED = 0;

    // 报警方式
    public static final int MONITOR_NOTIFY_TYPE_EMAIL = 1;
    public static final int MONITOR_NOTIFY_TYPE_FEISHU = 2;

}

package com.mega.platform.cloud.common.aop;

import com.mega.platform.cloud.core.utils.SpringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.annotation.AnnotationUtils;

import javax.annotation.PostConstruct;
import java.lang.reflect.Method;
import java.util.Map;
import java.util.concurrent.Executors;

@Slf4j
public class RedisConsumeManager {

    @PostConstruct
    public void init() {
        Map<String, Object> beans =
                SpringUtils.getApplicationContext().getBeansWithAnnotation(RedisConsume.class);
        for (Map.Entry<String, Object> entry : beans.entrySet()) {
            Object instance = entry.getValue();
            Method[] methods = instance.getClass().getDeclaredMethods();
            for (Method method : methods) {
                method.setAccessible(true);
                RedisConsumeListener annotation = AnnotationUtils.findAnnotation(method, RedisConsumeListener.class);
                if (annotation != null) {
                    Executors.newSingleThreadExecutor().execute(() -> {
                        try {
                            method.invoke(instance, (Object) null);
                        } catch (Exception e) {
                            log.error("Redis消费监听启动失败", e);
                        }
                    });
                }
            }
        }
    }
}

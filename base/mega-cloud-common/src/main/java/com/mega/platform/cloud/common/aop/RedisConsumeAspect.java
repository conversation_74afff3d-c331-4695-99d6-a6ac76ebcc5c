package com.mega.platform.cloud.common.aop;

import com.mega.platform.cloud.common.CommonConfig;
import com.mega.platform.cloud.core.utils.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import java.util.concurrent.TimeUnit;

@Component
@Aspect
@Order(-2)
@Slf4j
public class RedisConsumeAspect {

    private final StringRedisTemplate stringRedisTemplate;

    @Autowired
    public RedisConsumeAspect(StringRedisTemplate stringRedisTemplate) {
        this.stringRedisTemplate = stringRedisTemplate;
    }

    @Around("@annotation(listener)")
    public Object around(ProceedingJoinPoint joinPoint, RedisConsumeListener listener) {
        String redisList = listener.value() + CommonConfig.ENV;
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        Class<?> clazz = signature.getMethod().getParameterTypes()[0];
        while (true) {
            String value = null;
            try {
                value = this.stringRedisTemplate.opsForList().rightPop(redisList, 10, TimeUnit.SECONDS);
                if (StringUtils.isNotBlank(value)) {
                    log.info("消费" + redisList + "队列开始={}", value);
                    Object[] args = {JsonUtils.fromJson(value, clazz)};
                    joinPoint.proceed(args);
                    log.info("消费" + redisList + "队列成功={}", value);
                }
            } catch (Throwable throwable) {
                log.error("消费" + redisList + "队列异常={}", value, throwable);
            }
        }
    }
}

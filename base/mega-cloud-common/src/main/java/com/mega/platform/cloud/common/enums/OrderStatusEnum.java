package com.mega.platform.cloud.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum OrderStatusEnum {
    INIT(0, "初始化"),
    SUCCESS(1, "成功"),
    FAIL(2, "失败"),
    REFUNDED(3, "已退款");

    private final int code;
    private final String description;

    public static OrderStatusEnum fromCode(Long code) {
        for (OrderStatusEnum value : OrderStatusEnum.values()) {
            if (value.getCode() == code) {
                return value;
            }
        }
        return null;
    }
}

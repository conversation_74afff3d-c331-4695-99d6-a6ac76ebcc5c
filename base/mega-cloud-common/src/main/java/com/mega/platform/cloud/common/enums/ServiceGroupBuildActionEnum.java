package com.mega.platform.cloud.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum ServiceGroupBuildActionEnum {
    NOTHING(0, "nothing", ""), RESTART(1, "restart", "重启"), STOP(2, "stop", "停止"), START(3, "restart", "启动"),
    ;

    private final Integer action;
    private final String jenkinsRestartType;
    private final String showStr;

    public static ServiceGroupBuildActionEnum findByAction(Integer action) {
        for (ServiceGroupBuildActionEnum actionEnum : ServiceGroupBuildActionEnum.values()) {
            if (actionEnum.getAction().equals(action)) {
                return actionEnum;
            }
        }
        return null;
    }
}

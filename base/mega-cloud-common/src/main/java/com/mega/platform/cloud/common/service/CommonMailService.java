package com.mega.platform.cloud.common.service;

import com.mega.platform.cloud.data.dto.common.BaseMailDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class CommonMailService {

    private final InnerAsyncService innerAsyncService;

    public CommonMailService(InnerAsyncService innerAsyncService) {
        this.innerAsyncService = innerAsyncService;
    }

    public void sendMail(BaseMailDTO dto) {
        try {
            innerAsyncService.sendMailAsync(dto);
        } catch (Exception e) {
            log.error("发送邮件失败", e);
        }
    }
}

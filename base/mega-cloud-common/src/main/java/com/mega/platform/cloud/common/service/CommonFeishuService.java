package com.mega.platform.cloud.common.service;

import com.mega.platform.cloud.common.config.FeishuProperties;
import com.mega.platform.cloud.data.dto.common.MonitorAlarmRecoverNotifyFeishuDTO;
import com.mega.platform.cloud.data.dto.common.MonitorAlarmWarningNotifyFeishuDTO;
import com.mega.platform.cloud.data.dto.common.SlowSqlStatisticsFeishuDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class CommonFeishuService {
    private final FeishuProperties feishuProperties;
    private final InnerAsyncService innerAsyncService;

    @Autowired
    public CommonFeishuService(FeishuProperties feishuProperties, InnerAsyncService innerAsyncService) {
        this.feishuProperties = feishuProperties;
        this.innerAsyncService = innerAsyncService;
    }

    public void sendSlowSqlFeishuMessage(SlowSqlStatisticsFeishuDTO feishuDTO) {
        try {
            feishuDTO.addBotUrl(feishuProperties.getSlowSqlUrl());
            feishuDTO.addTag("慢查询");
            this.innerAsyncService.sendFeishuMessageAsync(feishuDTO);
        } catch (Exception e) {
            log.error("sendSlowSqlFeishuMessage异常", e);
        }
    }

    public void sendMonitorAlarmWarningFeishuMessage(MonitorAlarmWarningNotifyFeishuDTO feishuDTO) {
        try {
            feishuDTO.addTag("监控");
            this.innerAsyncService.sendFeishuMessageAsync(feishuDTO);
        } catch (Exception e) {
            log.error("sendMonitorAlarmFeishuMessage异常", e);
        }
    }

    public void sendMonitorAlarmRecoverFeishuMessage(MonitorAlarmRecoverNotifyFeishuDTO feishuDTO) {
        try {
            feishuDTO.addTag("监控");
            this.innerAsyncService.sendFeishuMessageAsync(feishuDTO);
        } catch (Exception e) {
            log.error("sendMonitorAlarmFeishuMessage异常", e);
        }
    }
}

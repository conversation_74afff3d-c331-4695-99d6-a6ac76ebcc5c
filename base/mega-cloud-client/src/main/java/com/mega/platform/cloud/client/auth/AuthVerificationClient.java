package com.mega.platform.cloud.client.auth;

import com.mega.platform.cloud.core.Result;
import com.mega.platform.cloud.data.vo.auth.*;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

@FeignClient(value = "mega-cloud-auth-" + "${spring.profiles.active}", contextId = "mega-cloud-auth-verification-client")
@RequestMapping("/auth/api/verification")
@Api(tags = "验证码接口")
public interface AuthVerificationClient {
    @ApiOperation("获取手机验证码")
    @PostMapping("/sms/code")
    public Result<AuthSendSmsCodeRespVO> sendSmsCode(@Validated @RequestBody AuthSendSmsCodeReqVO vo) throws Exception;

    @ApiOperation("验证手机验证码")
    @PostMapping("/sms/verify")
    public Result<Boolean> verifySmsCode(@Validated @RequestBody AuthVerifySmsCodeReqVO vo);

    @ApiOperation("验证苹果")
    @PostMapping("/apple/verify")
    public Result<AuthVerifyAppleTokenRespVO> verifyAppleToken(@Validated @RequestBody AuthVerifyAppleTokenReqVO vo);

    @ApiOperation("验证微信code")
    @PostMapping("/wechat/verify")
    public Result<AuthWeChatUserInfoRespVO> verifyWechatCode(@Validated @RequestBody AuthWeChatUserInfoReqVO vo);
}

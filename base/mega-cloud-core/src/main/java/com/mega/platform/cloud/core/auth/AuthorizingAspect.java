package com.mega.platform.cloud.core.auth;


import com.mega.platform.cloud.core.ResultCode;
import com.mega.platform.cloud.core.exception.BaseException;
import com.mega.platform.cloud.core.utils.SpringUtils;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

@Component
@Aspect
@Order(Ordered.HIGHEST_PRECEDENCE + 2)
public class AuthorizingAspect {

    @Before("@annotation(authorizing)")
    public void before(JoinPoint joinPoint, Authorizing authorizing) {
        AuthorizingRealm authorizingRealm = SpringUtils.getApplicationContext().getBean(authorizing.value());
        boolean authenticate = authorizingRealm.authenticate();
        if (!authenticate) {
            throw new BaseException(ResultCode.NOT_AUTHENTICATE_ERROR);
        }
        boolean authorize = authorizingRealm.authorize();
        if (!authorize) {
            throw new BaseException(ResultCode.NOT_AUTHORIZE_ERROR);
        }
    }
}

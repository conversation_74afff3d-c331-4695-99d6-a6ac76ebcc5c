package com.mega.platform.cloud.core;

import com.mega.platform.cloud.core.utils.SpringUtils;
import lombok.Getter;

@Getter
public enum ResultCode {

    SUCCESS(1),
    ERROR(0),
    NOT_AUTHENTICATE_ERROR(2),
    NOT_AUTHORIZE_ERROR(3),
    PARAM_ERROR(4),

    GET_CLIENT_IP_ERROR(100),
    ;

    private final Integer code;

    ResultCode(Integer code) {
        this.code = code;
    }

    public String getMessage() {
        return SpringUtils.getLocaleMessage(this.code);
    }
}

# Mega Cloud 项目规则目录

## 📋 规则文件索引

本目录包含 Mega Cloud 项目的所有开发规范和规则文档，确保团队开发的一致性和代码质量。

## 📁 规则文件列表

### 1. [项目结构.md](./项目结构.md)
**描述**: 项目架构和模块组织规范
**内容包括**:
- 项目架构概述
- 模块组织结构（基础模块、管理模块、第三方集成模块、微服务模块）
- API 路由规范
- 认证头规范
- 文件命名约定
- 配置文件结构
- 文档结构
- 目录结构示例

### 2. [编码规范.md](./编码规范.md)
**描述**: 代码开发规范和最佳实践
**内容包括**:
- 命名规范（类、方法、变量、VO等）
- Controller 开发规范
- Service 层开发规范
- VO 开发规范
- Entity 开发规范
- API 设计规范
- 依赖注入规范
- MyBatis 规范
- 常量管理规范

### 3. [技术栈.md](./技术栈.md)
**描述**: 技术选型和工具配置
**内容包括**:
- 核心框架（Spring Boot、Spring Cloud、Java、Maven）
- 数据库与持久化（MySQL、Redis、ClickHouse、MyBatis）
- 第三方服务集成（阿里云、融云、Sentry、钉钉）
- 开发工具（Lombok、Swagger、MyBatis Generator）
- 部署与基础设施（Docker、Harbor、监控）
- 关键依赖库
- 常用构建命令
- 开发环境配置

## 🎯 规则遵循原则

### 强制性规则 (MUST)
- 依赖注入必须使用构造函数注入
- VO 命名必须遵循模块前缀规范
- API 返回值必须使用 Result<T> 包装
- 对象比较必须使用 equals() 方法

### 推荐性规则 (SHOULD)
- 使用 Lombok 减少样板代码
- 使用 Swagger 注解生成 API 文档
- 遵循 RESTful API 设计原则
- 使用统一的异常处理机制

### 可选性规则 (MAY)
- 可以使用自定义工具类简化开发
- 可以根据业务需要扩展基础框架
- 可以使用缓存优化性能

## 📖 使用指南

### 新团队成员
1. 首先阅读 [项目结构.md](./项目结构.md) 了解整体架构
2. 然后学习 [编码规范.md](./编码规范.md) 掌握开发规范
3. 最后查看 [技术栈.md](./技术栈.md) 熟悉技术环境

### 开发过程中
1. 开发前检查相关规范要求
2. 代码审查时参考编码规范
3. 遇到技术问题时查阅技术栈文档

### 规范更新
1. 规范变更需要团队讨论确认
2. 更新后及时通知所有团队成员
3. 定期回顾和优化现有规范

## 🔄 规则维护

### 更新频率
- **项目结构**: 架构调整时更新
- **编码规范**: 发现新的最佳实践时更新
- **技术栈**: 技术升级或新增依赖时更新

### 维护责任
- **架构师**: 负责项目结构规范
- **技术负责人**: 负责编码规范和技术栈
- **全体开发人员**: 参与规范讨论和反馈

## 📞 联系方式

如有规范相关问题或建议，请联系：
- 技术负责人
- 架构师
- 或在团队群中讨论

## 📝 版本历史

| 版本 | 日期 | 更新内容 | 更新人 |
|------|------|----------|--------|
| 1.0.0 | 2025-01-XX | 初始版本，整理现有规则 | AI Assistant |

---

**注意**: 本规则目录基于 `.augment/rules/` 目录下的原始规则文件整理而成，旨在提供更清晰的结构和更好的可读性。

# mega-cloud-access模块新需求

mega-cloud-access需要新增`PartnerController`来提供给合作方调用的接口。
并生成对应的`PartnerService`和`PartnerDao`以及mybatis xml文件

新增以下接口：
### 1 从远程服务获得AES密钥
路由 `/access/api/partner/public/verify`

入参：
tokenKey: string
innerIp 内网ip
publicIp 公网ip
machineId: 基于机器指纹生成（CPU序列号+主板信息+MAC地址等）
md5List<Md5VO> 文件的md5列表，防止文件篡改

类Md5VO 包含字段：
* fileName 文件名
* md5 文件md5

出参：
密钥

逻辑：
根据tokenKey查询表`project_app`获得id作为 project_app_id，再根据`project_app_id`查询表`access_partner_crypto`获得crypto_key和md5_json。
如果md5_json不为空，则与入参的md5列表比对，如果md5不一致，则返回md5认证错误（需要在AccessErrorCode.java与messages.properties新增错误类型）。
如果md5_json为空，返回crypto_key作为密钥


### 2 从请求远程服务验证license
路由 `/access/api/partner/public/license`
入参：
tokenKey: string 
license: "UUID"
innerIp 内网ip
publicIp 公网ip
machineId: 基于机器指纹生成（CPU序列号+主板信息+MAC地址等）
md5List: 文件的md5列表，防止文件篡改

出参:
无

逻辑：
1. 根据tokenKey查询表`project_app`获得id作为 project_app_id，在根据入参的machineId查询access_partner_license_active表获得数据。
2. 数据不存在，则为首次激活，写入表access_partner_license_active和access_partner_license_active_record
3. 数据存在，如果表中machine_id


错误直接推出程序

### 3 上报远程服务器操作日志
路由 `/access/api/partner/public/telemetering`
入参：
ExchangeTransactionReqVO

出参:
无

### 表结构相关
## 1. access_partner_license_active (合作方License激活状态表)

```sql
CREATE TABLE `access_partner_license_active` (
  `project_app_id` bigint(20) NOT NULL COMMENT '项目应用ID',
  `machine_id` varchar(64) NOT NULL COMMENT '机器唯一标识',
  `license` varchar(255) NOT NULL COMMENT 'License授权码',
  `state` tinyint(4) NOT NULL DEFAULT '0' COMMENT '状态：0-未激活 1-已激活',
  `server_name` varchar(64) NOT NULL COMMENT '服务名称，如gossipharbor-cloud-exchange',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `delsign` tinyint(4) NOT NULL DEFAULT '0' COMMENT '删除标记：0-未删除 1-已删除',
  PRIMARY KEY (`project_app_id`, `machine_id`, `serverName`),
  KEY `idx_license` (`license`),
  KEY `idx_state` (`state`),
  KEY `idx_delsign` (`delsign`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='合作方License激活状态表';
```

## 2. access_partner_license_active_record (合作方License激活记录表)

```sql
CREATE TABLE `access_partner_license_active_record` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `project_app_id` bigint(20) NOT NULL COMMENT '项目应用ID',
  `inner_ip` varchar(64) DEFAULT NULL COMMENT '内网IP地址',
  `public_ip` varchar(64) DEFAULT NULL COMMENT '公网IP地址',
  `machine_id` varchar(64) NOT NULL COMMENT '机器唯一标识',
  `license` varchar(255) NOT NULL COMMENT 'License授权码',
  `success` tinyint(4) NOT NULL DEFAULT '0' COMMENT '激活结果：0-失败 1-成功',
  `fail_reason` varchar(255) DEFAULT NULL COMMENT '失败原因',
  `serverName` varchar(64) NOT NULL COMMENT '服务名称，如gossipharbor-cloud-exchange',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_project_app_id` (`project_app_id`),
  KEY `idx_machine_id` (`machine_id`),
  KEY `idx_license` (`license`),
  KEY `idx_success` (`success`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='合作方License激活记录表';
```

## 3. access_partner_crypto (合作方加密信息表)

```sql
CREATE TABLE `access_partner_crypto` (
  `project_app_id` bigint(20) NOT NULL COMMENT '项目应用ID',
  `server_name` varchar(64) NOT NULL COMMENT '服务名称，如gossipharbor-cloud-exchange',
  `crypto_key` varchar(512) NOT NULL COMMENT '加密密钥',
  `md5_json` text COMMENT '存储MD5信息的JSON',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `delsign` tinyint(4) NOT NULL DEFAULT '0' COMMENT '删除标记：0-未删除 1-已删除',
  PRIMARY KEY (`project_app_id`, `serverName`),
  KEY `idx_delsign` (`delsign`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='合作方加密信息表';
```

<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.mega.platform.cloud.microservice.dao.ServicesDao">
    <insert id="insertServices" useGeneratedKeys="true" keyColumn="id" keyProperty="services.id">
        INSERT INTO services (name, services_group_id, ecs_server_id, jenkins_job_id)
        VALUES (#{services.name}, #{services.servicesGroupId}, #{services.ecsServerId}, #{services.jenkinsJobId})
    </insert>
    <insert id="insertServicesGroup" useGeneratedKeys="true" keyColumn="id" keyProperty="servicesGroup.id">
        INSERT INTO services_group (name, project_id, project_app_id, services_update_type, services_env, services_alive_num, jenkins_services_id, jenkins_template_id, admin_user_id, check_consul)
        VALUES (#{servicesGroup.name}, #{servicesGroup.projectAppId}, #{servicesGroup.projectAppId}, #{servicesGroup.servicesUpdateType}, #{servicesGroup.servicesEnv}, #{servicesGroup.servicesAliveNum}, #{servicesGroup.jenkinsServicesId},
                #{servicesGroup.jenkinsTemplateId}, #{servicesGroup.adminUserId}, #{servicesGroup.checkConsul})
    </insert>
    <select id="getServicesEcsServerPrivateIpByServicesId" resultType="java.lang.String">
        SELECT t2.private_ip AS serverIp
        FROM services AS t1
                 LEFT JOIN ecs_server AS t2 ON t1.ecs_server_id = t2.id
        WHERE t1.id = #{servicesId}
          AND t1.delsign = 0
    </select>
    <select id="getRunningServicesNumByServicesGroupId" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM services
        WHERE services_group_id = #{servicesGroupId}
          AND status = 1
          AND delsign = 0
          AND running_status = 1
    </select>
    <select id="getSameServicesNumByEcsServerIdAndPort" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM jenkins_job_template_param_value AS t1
                 LEFT JOIN jenkins_job_template_param AS t2 ON t1.jenkins_job_templete_param_id = t2.id
                 LEFT JOIN services AS t3 ON t1.services_data_id = t3.id AND t2.services_data_type = 2
        WHERE t2.param_key = 'port'
          AND t3.ecs_server_id = #{ecsServerId}
          AND t1.param_value = #{port}
          AND t1.delsign = 0
          AND t2.delsign = 0
          AND t3.delsign = 0
    </select>
</mapper>
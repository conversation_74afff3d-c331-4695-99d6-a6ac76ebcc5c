package com.mega.platform.cloud.microservice.controller;

import com.mega.platform.cloud.core.Result;
import com.mega.platform.cloud.core.Results;
import com.mega.platform.cloud.data.dto.microservice.BuildServicesDTO;
import com.mega.platform.cloud.data.vo.microservice.BuildServicesGroupReqVO;
import com.mega.platform.cloud.microservice.service.ServicesService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Api(tags = "test")
@Slf4j
@RestController
@RequestMapping("/microservice/api/test")
public class TestController {

    private final ServicesService servicesService;

    @Autowired
    public TestController(ServicesService servicesService) {
        this.servicesService = servicesService;
    }

    @ApiOperation("test")
    @PostMapping("/testBuildServicesGroup")
    public Result<?> testBuildServicesGroup(@Validated @RequestBody BuildServicesGroupReqVO vo) throws Exception {
        BuildServicesDTO dto = new BuildServicesDTO();
        BeanUtils.copyProperties(vo, dto);
        servicesService.buildServicesGroup(dto);
        return Results.success();
    }
}

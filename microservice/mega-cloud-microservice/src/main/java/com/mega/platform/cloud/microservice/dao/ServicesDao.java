package com.mega.platform.cloud.microservice.dao;

import com.mega.platform.cloud.data.entity.Services;
import com.mega.platform.cloud.data.entity.ServicesGroup;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface ServicesDao {
    void insertServices(@Param("services") Services services);

    void insertServicesGroup(@Param("servicesGroup") ServicesGroup servicesGroup);

    String getServicesEcsServerPrivateIpByServicesId(@Param("servicesId") Long servicesId);

    Integer getRunningServicesNumByServicesGroupId(@Param("servicesGroupId") Long servicesGroupId);

    Integer getSameServicesNumByEcsServerIdAndPort(@Param("ecsServerId") Long ecsServerId, @Param("port") Integer port);
}
package com.mega.platform.cloud;

import com.mega.platform.cloud.microservice.service.ServicesService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.properties.ConfigurationPropertiesScan;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import tk.mybatis.spring.annotation.MapperScan;

@SpringBootApplication
@EnableDiscoveryClient
@ConfigurationPropertiesScan
@MapperScan(basePackages = {"com.mega.**.mapper", "com.mega.**.dao"})
public class MicroServiceApplication implements CommandLineRunner {

    private final ServicesService servicesService;

    @Autowired
    public MicroServiceApplication(ServicesService servicesService) {
        this.servicesService = servicesService;
    }

    public static void main(String[] args) {
        SpringApplication.run(MicroServiceApplication.class, args);
    }

    @Override
    public void run(String... args) throws Exception {
        servicesService.reloadUncompletedJenkinsTask();
    }
}

package com.mega.platform.cloud;


import com.mega.platform.cloud.core.exception.BaseException;

import java.util.Objects;

public class MicroServiceException extends BaseException {

    public MicroServiceException(Integer code) {
        this(Objects.requireNonNull(MicroServiceErrorCode.getExchangeCode(code)));
    }

    public MicroServiceException(MicroServiceErrorCode code) {
        super(code.getCode(), code.getMessage());
    }

    public MicroServiceException(Integer code, String message) {
        super(code, message);
    }
}

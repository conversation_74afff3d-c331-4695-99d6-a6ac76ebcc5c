package com.mega.platform.cloud.microservice.service;

import com.mega.platform.cloud.common.enums.ServiceGroupBuildActionEnum;
import com.mega.platform.cloud.common.utils.ServicesCheckUtils;
import com.mega.platform.cloud.data.dto.jenkins.BaseJenkinsDTO;
import com.mega.platform.cloud.data.dto.jenkins.BuildJenkinsJobDTO;
import com.mega.platform.cloud.data.dto.jenkins.JenkinsTaskDTO;
import com.mega.platform.cloud.microservice.service.jenkins.JenkinsApiClient;
import com.offbytwo.jenkins.model.BuildResult;
import com.offbytwo.jenkins.model.BuildWithDetails;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import static com.mega.platform.cloud.common.constant.MicroserviceConstants.*;

@Service
@Slf4j
public class RestartService {

    private final JenkinsApiClient jenkinsApiClient;
    private final MicroserviceCommonService microserviceCommonService;

    @Autowired
    public RestartService(JenkinsApiClient jenkinsApiClient, MicroserviceCommonService microserviceCommonService) {
        this.jenkinsApiClient = jenkinsApiClient;
        this.microserviceCommonService = microserviceCommonService;
    }


    public boolean beforeDoTask(JenkinsTaskDTO taskDTO) {
        // 导流重启 要等待服务器返回可关闭之后才能操作
        try {
            if (taskDTO.getServicesUpdateType().equals(SERVICES_GROUP_UPDATE_TYPE_ROLLING_WAIT) && !taskDTO.getRealAction().equals(ServiceGroupBuildActionEnum.START.getAction())) {
                Boolean notifySuccess = ServicesCheckUtils.notifyServicesStop(taskDTO.getServerIp(), taskDTO.getServicesPort());
                if (!notifySuccess) {
                    microserviceCommonService.logTaskContent(taskDTO, "通知业务服务器重启失败");
                    log.info("RestartService beforeDoTask notifyServicesStop failed, taskGroupId {}, taskId {}", taskDTO.getJenkinsTaskGroupId(), taskDTO.getJenkinsTaskId());
                    return false;
                }
                microserviceCommonService.logTaskContent(taskDTO, "通知业务服务器重启成功");
                log.info("RestartService beforeDoTask notifyServicesStop success, taskGroupId {}, taskId {}", taskDTO.getJenkinsTaskGroupId(), taskDTO.getJenkinsTaskId());
                microserviceCommonService.updateServicesRunningStatus(taskDTO.getServicesId(), SERVICES_RUNNING_STATUS_WAITING_STOP);
                Boolean canStop = false;
                while (!canStop) {
                    Thread.sleep(5000);
                    canStop = ServicesCheckUtils.checkServicesCanStop(taskDTO.getServerIp(), taskDTO.getServicesPort());;
                    microserviceCommonService.logTaskContent(taskDTO, "查询业务服务器可关闭状态: %s", canStop);
                    if (!canStop) {
                        log.info("RestartService beforeDoTask checkServiceCanStop doing, taskGroupId {}, taskId {}", taskDTO.getJenkinsTaskGroupId(), taskDTO.getJenkinsTaskId());
                    }
                }
                microserviceCommonService.updateServicesRunningStatus(taskDTO.getServicesId(), SERVICES_RUNNING_STATUS_BUILDING);
                microserviceCommonService.logTaskContent(taskDTO, "查询业务服务器可关闭状态: %s", canStop);
                log.info("RestartService beforeDoTask checkServiceCanStop success, taskGroupId {}, taskId {}", taskDTO.getJenkinsTaskGroupId(), taskDTO.getJenkinsTaskId());
                return true;
            }
            return true;
        } catch (Exception e) {
            microserviceCommonService.logTaskContent(taskDTO, "通知/查询业务服务器异常: %s", e.getMessage());
            microserviceCommonService.updateServicesRunningStatus(taskDTO.getServicesId(), SERVICES_RUNNING_STATUS_BUILD_ERROR);
            log.warn("RestartService afterDoTask checkServiceIsRunning error, taskGroupId {}, taskId {}", taskDTO.getJenkinsTaskGroupId(), taskDTO.getJenkinsTaskId(), e);
            return false;
        }
    }

    public boolean doTask(JenkinsTaskDTO taskDTO, BaseJenkinsDTO baseDto) throws Exception {
        try {
            microserviceCommonService.logTaskContent(taskDTO, "构建任务参数列表: %s", taskDTO.getJenkinsTaskParams());
            BuildJenkinsJobDTO dto = new BuildJenkinsJobDTO(taskDTO.getJenkinsJobName(), taskDTO.getJenkinsTaskParams());
            Integer buildId = jenkinsApiClient.buildJenkinsJob(dto, baseDto);
            microserviceCommonService.logTaskContent(taskDTO, "构建任务开始");
            log.info("RestartService doTask buildJenkinsJob success, taskGroupId {}, taskId {}, buildId {}", taskDTO.getJenkinsTaskGroupId(), taskDTO.getJenkinsTaskId(), buildId);
            BuildResult result = null;
            long startTs = System.currentTimeMillis();
            while (result == null && System.currentTimeMillis() - startTs < 3600000) {
                Thread.sleep(5000);
                BuildWithDetails jenkinsJobBuildStatus = jenkinsApiClient.getJenkinsJobBuildStatus(taskDTO.getJenkinsJobName(), baseDto, buildId);
                result = jenkinsJobBuildStatus.getResult();
                if (result == null) {
                    microserviceCommonService.logTaskContent(taskDTO, "构建任务执行中...");
                    log.info("RestartService doTask getJenkinsJobBuildStatus doing, taskGroupId {}, taskId {}, buildId {}", taskDTO.getJenkinsTaskGroupId(), taskDTO.getJenkinsTaskId(), buildId);
                }
            }
            if (result == null) {
                microserviceCommonService.logTaskContent(taskDTO, "构建任务执行超时");
                microserviceCommonService.updateServicesRunningStatus(taskDTO.getServicesId(), SERVICES_RUNNING_STATUS_BUILD_ERROR);
                log.info("RestartService doTask getJenkinsJobBuildStatus timeout, taskGroupId {}, taskId {}, buildId {}", taskDTO.getJenkinsTaskGroupId(), taskDTO.getJenkinsTaskId(), buildId);
                return false;
            }
            if (result.equals(BuildResult.SUCCESS)) {
                microserviceCommonService.logTaskContent(taskDTO, "构建任务执行完成");
                log.info("RestartService doTask getJenkinsJobBuildStatus success, taskGroupId {}, taskId {}", taskDTO.getJenkinsTaskGroupId(), taskDTO.getJenkinsTaskId());
                return true;
            } else {
                microserviceCommonService.logTaskContent(taskDTO, "构建任务执行失败，任务结果: %s", result);
                microserviceCommonService.updateServicesRunningStatus(taskDTO.getServicesId(), SERVICES_RUNNING_STATUS_BUILD_ERROR);
                log.info("RestartService doTask getJenkinsJobBuildStatus failed, result{}, taskGroupId {}, taskId {}",result, taskDTO.getJenkinsTaskGroupId(), taskDTO.getJenkinsTaskId());
                return false;
            }
        } catch (Exception e) {
            microserviceCommonService.logTaskContent(taskDTO, "构建任务执行异常，原因: %s", e.getMessage());
            microserviceCommonService.updateServicesRunningStatus(taskDTO.getServicesId(), SERVICES_RUNNING_STATUS_BUILD_ERROR);
            log.warn("RestartService doTask getJenkinsJobBuildStatus error, taskGroupId {}, taskId {}", taskDTO.getJenkinsTaskGroupId(), taskDTO.getJenkinsTaskId(), e);
            return false;
        }
    }

    public boolean afterDoTask(JenkinsTaskDTO taskDTO) throws Exception {
        try {
            if (taskDTO.getServicesUpdateType().equals(SERVICES_GROUP_UPDATE_TYPE_SCRIPT)) {
                // 脚本不需要执行状态检查
                return true;
            }
            if (taskDTO.getRealAction().equals(ServiceGroupBuildActionEnum.STOP.getAction())) {
                // STOP指令 检查30秒 是否关闭成功
                long startTs = System.currentTimeMillis();
                Boolean isRunning = true;
                while (isRunning && System.currentTimeMillis() - startTs < 30000) {
                    Thread.sleep(5000);
                    isRunning = ServicesCheckUtils.checkServiceIsRunning(taskDTO.getServerIp(), taskDTO.getServicesPort());
                    if (isRunning) {
                        microserviceCommonService.logTaskContent(taskDTO, "检查业务是否启动: %s", isRunning);
                        log.info("RestartService afterDoTask checkServiceIsRunning doing, taskGroupId {}, taskId {}", taskDTO.getJenkinsTaskGroupId(), taskDTO.getJenkinsTaskId());
                    }
                }
                if (!isRunning) {
                    microserviceCommonService.logTaskContent(taskDTO, "检查业务是否启动: %s", isRunning);
                    microserviceCommonService.updateServicesRunningStatus(taskDTO.getServicesId(), SERVICES_RUNNING_STATUS_NOT_RUNNING);
                    log.info("RestartService doTask checkServiceIsRunning success, taskGroupId {}, taskId {}", taskDTO.getJenkinsTaskGroupId(), taskDTO.getJenkinsTaskId());
                    return true;
                } else {
                    microserviceCommonService.logTaskContent(taskDTO, "检查业务是否启动超时");
                    microserviceCommonService.updateServicesRunningStatus(taskDTO.getServicesId(), SERVICES_RUNNING_STATUS_RUNNING);
                    log.warn("RestartService doTask checkServiceIsRunning timeout, taskGroupId {}, taskId {}", taskDTO.getJenkinsTaskGroupId(), taskDTO.getJenkinsTaskId());
                    return false;
                }
            } else {
                // RESTART START 检查600秒 是否启动成功
                long startTs = System.currentTimeMillis();
                Boolean isRunning = false;
                while (!isRunning && System.currentTimeMillis() - startTs < 600000) {
                    Thread.sleep(5000);
                    isRunning = ServicesCheckUtils.checkServiceIsRunning(taskDTO.getServerIp(), taskDTO.getServicesPort());
                    if (!isRunning) {
                        microserviceCommonService.logTaskContent(taskDTO, "检查业务是否启动: %s", isRunning);
                        log.info("RestartService afterDoTask checkServiceIsRunning doing, taskGroupId {}, taskId {}", taskDTO.getJenkinsTaskGroupId(), taskDTO.getJenkinsTaskId());
                    }
                }
                if (isRunning) {
                    microserviceCommonService.logTaskContent(taskDTO, "检查业务是否启动: %s", isRunning);
                    microserviceCommonService.updateServicesRunningStatus(taskDTO.getServicesId(), SERVICES_RUNNING_STATUS_RUNNING);
                    log.info("RestartService doTask checkServiceIsRunning success, taskGroupId {}, taskId {}", taskDTO.getJenkinsTaskGroupId(), taskDTO.getJenkinsTaskId());
                } else {
                    microserviceCommonService.logTaskContent(taskDTO, "检查业务是否启动超时");
                    microserviceCommonService.updateServicesRunningStatus(taskDTO.getServicesId(), SERVICES_RUNNING_STATUS_NOT_RUNNING);
                    log.warn("RestartService doTask checkServiceIsRunning timeout, taskGroupId {}, taskId {}", taskDTO.getJenkinsTaskGroupId(), taskDTO.getJenkinsTaskId());
                    return false;
                }
                if (taskDTO.getCheckConsul()) {
                    startTs = System.currentTimeMillis();
                    Boolean consulUpStatus = false;
                    while (!consulUpStatus && System.currentTimeMillis() - startTs < 600000) {
                        Thread.sleep(5000);
                        consulUpStatus = ServicesCheckUtils.checkServiceConsulUp(taskDTO.getServerIp(), taskDTO.getServicesPort());
                        if (!consulUpStatus) {
                            microserviceCommonService.logTaskContent(taskDTO, "检查业务是否注册consul: %s", consulUpStatus);
                            log.info("RestartService afterDoTask checkServiceConsulUp doing, taskGroupId {}, taskId {}", taskDTO.getJenkinsTaskGroupId(), taskDTO.getJenkinsTaskId());
                        }
                    }
                    if (consulUpStatus) {
                        microserviceCommonService.logTaskContent(taskDTO, "检查业务是否注册consul: %s", isRunning);
                        microserviceCommonService.updateServicesRunningStatus(taskDTO.getServicesId(), SERVICES_RUNNING_STATUS_RUNNING);
                        log.info("RestartService doTask checkServiceConsulUp success, taskGroupId {}, taskId {}", taskDTO.getJenkinsTaskGroupId(), taskDTO.getJenkinsTaskId());
                        return true;
                    } else {
                        microserviceCommonService.logTaskContent(taskDTO, "检查业务是否注册consul超时");
                        microserviceCommonService.updateServicesRunningStatus(taskDTO.getServicesId(), SERVICES_RUNNING_STATUS_NOT_RUNNING);
                        log.warn("RestartService doTask checkServiceConsulUp timeout, taskGroupId {}, taskId {}", taskDTO.getJenkinsTaskGroupId(), taskDTO.getJenkinsTaskId());
                        return false;
                    }
                }
                return true;
            }

        } catch (Exception e) {
            microserviceCommonService.logTaskContent(taskDTO, "检查业务是否启动异常: %s", e.getMessage());
            microserviceCommonService.updateServicesRunningStatus(taskDTO.getServicesId(), SERVICES_RUNNING_STATUS_BUILD_ERROR);
            log.warn("RestartService afterDoTask checkServiceIsRunning/checkServiceConsulUp error, taskGroupId {}, taskId {}", taskDTO.getJenkinsTaskGroupId(), taskDTO.getJenkinsTaskId(), e);
            return false;
        }
    }

}

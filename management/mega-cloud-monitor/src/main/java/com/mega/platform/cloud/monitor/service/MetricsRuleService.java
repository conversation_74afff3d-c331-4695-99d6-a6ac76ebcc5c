package com.mega.platform.cloud.monitor.service;

import com.mega.platform.cloud.common.mapper.MonitorAlarmMapper;
import com.mega.platform.cloud.common.mapper.MonitorNotifyMapper;
import com.mega.platform.cloud.common.mapper.MonitorRuleMapper;
import com.mega.platform.cloud.common.service.CommonFeishuService;
import com.mega.platform.cloud.common.service.CommonMailService;
import com.mega.platform.cloud.core.utils.DateUtils;
import com.mega.platform.cloud.data.dto.common.MonitorAlarmRecoverNotifyFeishuDTO;
import com.mega.platform.cloud.data.dto.common.MonitorAlarmWarningNotifyFeishuDTO;
import com.mega.platform.cloud.data.dto.monitor.MetricsAlarmDTO;
import com.mega.platform.cloud.data.dto.monitor.MetricsNotifyDTO;
import com.mega.platform.cloud.data.dto.monitor.MetricsRuleDTO;
import com.mega.platform.cloud.data.entity.MonitorAlarm;
import com.mega.platform.cloud.data.entity.MonitorRule;
import com.mega.platform.cloud.monitor.dao.MetricsDao;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.stream.Collectors;

import static com.mega.platform.cloud.common.constant.CommonConstant.BYTE_1;
import static com.mega.platform.cloud.common.constant.MonitorConstants.*;

@Slf4j
@Service
public class MetricsRuleService {

    private final MetricsDao metricsDao;
    private final MonitorRuleMapper monitorRuleMapper;
    private final MongoTemplate mongoTemplate;
    private final MonitorCommonService monitorCommonService;
    private final MonitorAlarmMapper monitorAlarmMapper;
    private final CommonMailService commonMailService;
    private final CommonFeishuService commonFeishuService;

    @Autowired
    public MetricsRuleService(MetricsDao metricsDao, MonitorRuleMapper monitorRuleMapper, MongoTemplate mongoTemplate, MonitorCommonService monitorCommonService, MonitorAlarmMapper monitorAlarmMapper, MonitorNotifyMapper monitorNotifyMapper, CommonMailService commonMailService,
                              CommonFeishuService commonFeishuService) {
        this.metricsDao = metricsDao;
        this.monitorRuleMapper = monitorRuleMapper;
        this.mongoTemplate = mongoTemplate;
        this.monitorCommonService = monitorCommonService;
        this.monitorAlarmMapper = monitorAlarmMapper;
        this.commonMailService = commonMailService;
        this.commonFeishuService = commonFeishuService;
    }

    /**
     * 根据规则检查指标
     *
     * @param rule
     */
    public void checkRule(MetricsRuleDTO rule) {
        if (shouldExecute(rule)) {
            // 查询指标数据
            List<BigDecimal> metricsValues = queryMetricsValues(rule);
            // 计算触发规则指标占比
            BigDecimal matchRatio = metricsMatchRatio(metricsValues, rule);
            if (matchRatio.compareTo(rule.getDurationRatio()) <= 0) {
                // 指标正常
                log.info("checkRule ruleId={} 指标正常, 占比 {} <= {}", rule.getId(), matchRatio, rule.getDurationRatio());
                monitorRuleMapper.updateByPrimaryKeySelective(new MonitorRule().setId(rule.getId()).setStatus(MONITOR_RULE_STATUS_NORMAL).setLastCheckedTime(new Date()));
            } else {
                // 指标异常
                log.warn("checkRule ruleId={} 指标异常, 占比 {} > {}", rule.getId(), matchRatio, rule.getDurationRatio());
                monitorRuleMapper.updateByPrimaryKeySelective(new MonitorRule().setId(rule.getId()).setStatus(MONITOR_RULE_STATUS_WARNING).setLastCheckedTime(new Date()));
                MonitorAlarm alarm = new MonitorAlarm();
                alarm.setMonitorRuleId(rule.getId());
                alarm.setMonitorMetricId(rule.getMonitorMetricsId());
                alarm.setActualValue(metricsValues.get(metricsValues.size() - 1));
                alarm.setTriggerRatio(matchRatio);
                alarm.setStatus(MONITOR_ALARM_STATUS_NO_DEAL);
                alarm.setTriggerTime(new Date());
                alarm.setAlarmNum(0);
                metricsDao.insertMonitorAlarm(alarm);
                MetricsAlarmDTO alarmDTO = new MetricsAlarmDTO();
                BeanUtils.copyProperties(alarm, alarmDTO);
                alarmDTO.setAlarmIntervalSecond(rule.getAlarmIntervalSecond());
                alarmDTO.setCurrentRatio(matchRatio);
                alarmWarningNotify(alarmDTO);
            }
        } else {
            log.info("checkRule ruleId={} 未到扫描时间, 下次扫描时间: {}", rule.getId(), DateUtils.formatTime(DateUtils.addSeconds(rule.getLastCheckedTime(), rule.getScanIntervalSecond())));
        }
    }

    /**
     * 检查报警是否回复
     *
     * @param alarmDTO
     */
    public void checkAlarm(MetricsAlarmDTO alarmDTO) {
        Long ruleId = alarmDTO.getMonitorRuleId();
        MetricsRuleDTO rule = metricsDao.getMetricsRuleDTOByRuleId(ruleId);
        // 查询指标数据
        List<BigDecimal> metricsValues = queryMetricsValues(rule);
        // 计算触发规则指标占比
        BigDecimal matchRatio = metricsMatchRatio(metricsValues, rule);
        alarmDTO.setCurrentRatio(matchRatio);
        if (matchRatio.compareTo(rule.getDurationRatio()) > 0) {
            // 指标持续异常
            log.warn("checkRule alarmId={} 指标持续异常, 占比 {} > {}", alarmDTO.getMonitorRuleId(), matchRatio, rule.getDurationRatio());
            monitorRuleMapper.updateByPrimaryKeySelective(new MonitorRule().setId(rule.getId()).setLastCheckedTime(new Date()));
            monitorAlarmMapper.updateByPrimaryKeySelective(new MonitorAlarm().setId(alarmDTO.getId()).setTriggerRatio(matchRatio).setActualValue(metricsValues.get(metricsValues.size() - 1)));
            alarmWarningNotify(alarmDTO);
        } else {
            // 指标恢复正常
            log.warn("checkRule alarmId={} 指标恢复正常, 占比 {} <= {}", alarmDTO.getMonitorRuleId(), matchRatio, rule.getDurationRatio());
            monitorRuleMapper.updateByPrimaryKeySelective(new MonitorRule().setId(rule.getId()).setStatus(MONITOR_RULE_STATUS_NORMAL).setLastCheckedTime(new Date()));
            monitorAlarmMapper.updateByPrimaryKeySelective(new MonitorAlarm().setId(alarmDTO.getId()).setStatus(MONITOR_ALARM_STATUS_RECOVERED).setRecoverTime(new Date()).setTriggerRatio(matchRatio).setActualValue(metricsValues.get(metricsValues.size() - 1)));
            alarmRecoverNotify(alarmDTO);
        }
    }

    /**
     * 发送报警通知
     *
     * @param alarmDTO
     */
    private void alarmWarningNotify(MetricsAlarmDTO alarmDTO) {
        if (shouldNotify(alarmDTO)) {
            alarmDTO.setAlarmNum(alarmDTO.getAlarmNum() + 1);
            List<MetricsNotifyDTO> notifyDTOS = metricsDao.getMetricsNotifyDTOByRuleId(alarmDTO.getMonitorRuleId());
            for (MetricsNotifyDTO notifyDTO : notifyDTOS) {
                if (notifyDTO.getNotifyType() == MONITOR_NOTIFY_TYPE_EMAIL) {
                    // 邮件报警 TODO
                }
                if (notifyDTO.getNotifyType() == MONITOR_NOTIFY_TYPE_FEISHU) {
                    // 飞书报警
                    MonitorAlarmWarningNotifyFeishuDTO feishuDTO = new MonitorAlarmWarningNotifyFeishuDTO(alarmDTO, notifyDTO);
                    commonFeishuService.sendMonitorAlarmWarningFeishuMessage(feishuDTO);
                }
            }
            monitorAlarmMapper.updateByPrimaryKeySelective(new MonitorAlarm().setId(alarmDTO.getId()).setAlarmNum(alarmDTO.getAlarmNum()).setLastAlarmTime(new Date()));
            log.info("alarmWarningNotify alarmId={} 发送通知", alarmDTO.getId());
        } else {
            log.info("alarmWarningNotify alarmId={} 未到发送时间, 下次发送时间: {}", alarmDTO.getId(), DateUtils.formatTime(DateUtils.addSeconds(alarmDTO.getLastAlarmTime(), alarmDTO.getAlarmIntervalSecond())));
        }
    }

    /**
     * 发送恢复通知
     *
     * @param alarmDTO
     */
    private void alarmRecoverNotify(MetricsAlarmDTO alarmDTO) {
        List<MetricsNotifyDTO> notifyDTOS = metricsDao.getMetricsNotifyDTOByRuleId(alarmDTO.getMonitorRuleId());
        for (MetricsNotifyDTO notify : notifyDTOS) {
            // 判断是否需要恢复通知
            if (notify.getNotifyOnRecover().equals(BYTE_1)) {
                if (notify.getNotifyType() == MONITOR_NOTIFY_TYPE_EMAIL) {
                    // 邮件报警 TODO
                }
                if (notify.getNotifyType() == MONITOR_NOTIFY_TYPE_FEISHU) {
                    // 飞书报警
                    MonitorAlarmRecoverNotifyFeishuDTO feishuDTO = new MonitorAlarmRecoverNotifyFeishuDTO(alarmDTO, notify);
                    commonFeishuService.sendMonitorAlarmRecoverFeishuMessage(feishuDTO);
                }
            }
        }
        monitorAlarmMapper.updateByPrimaryKeySelective(new MonitorAlarm().setId(alarmDTO.getId()).setLastAlarmTime(new Date()));
        log.info("alarmRecoverNotify alarmId={} 发送通知", alarmDTO.getId());
    }

    /**
     * 查询指标数据
     *
     * @param ruleDTO
     * @return
     */
    private List<BigDecimal> queryMetricsValues(MetricsRuleDTO ruleDTO) {
        // 查询机器指标数据
        Query query = new Query();
        Date endTime = new Date();
        Date startTime = DateUtils.addSeconds(endTime, -ruleDTO.getDurationSecond());
        query.addCriteria(Criteria.where("collectTime").gte(startTime).lte(endTime));
        query.addCriteria(Criteria.where("sourceId").is(ruleDTO.getSourceId()));
        query.addCriteria(Criteria.where(ruleDTO.getMetricsKey()).ne(null));
        query.with(Sort.by(Sort.Direction.ASC, "collectTime"));
        List<HashMap> metricsDatas = mongoTemplate.find(query, HashMap.class, monitorCommonService.getMetricsDataCollectionName(ruleDTO.getSourceType(), ruleDTO.getMetricsType()));
        // 动态读key获取所有value
        List<BigDecimal> metricsValues = metricsDatas.stream().map(data -> new BigDecimal(data.get(ruleDTO.getMetricsKey()).toString())).collect(Collectors.toList());
        return metricsValues;
    }

    /**
     * 满足条件占比
     *
     * @param values
     * @param ruleDTO
     * @return
     */
    private BigDecimal metricsMatchRatio(List<BigDecimal> values, MetricsRuleDTO ruleDTO) {
        long matchCount = values.stream().filter(val -> compare(val, ruleDTO.getThresholdOperator(), ruleDTO.getThresholdValue())).count();
        BigDecimal count = BigDecimal.valueOf(matchCount);
        BigDecimal total = BigDecimal.valueOf(values.size());
        if (total.compareTo(BigDecimal.ZERO) == 0) {
            return BigDecimal.ZERO;
        }
        return count.divide(total, 4, RoundingMode.HALF_UP); // 保留4位小数
    }

    /**
     * 参数值，运算符，目标值 判定
     *
     * @param val
     * @param op
     * @param target
     * @return
     */
    private boolean compare(BigDecimal val, Integer op, BigDecimal target) {
        switch (op) {
            case METRICS_THRESHOLD_OPERATOR_GT:
                return val.compareTo(target) > 0;
            case METRICS_THRESHOLD_OPERATOR_GE:
                return val.compareTo(target) >= 0;
            case METRICS_THRESHOLD_OPERATOR_LT:
                return val.compareTo(target) < 0;
            case METRICS_THRESHOLD_OPERATOR_LE:
                return val.compareTo(target) <= 0;
            case METRICS_THRESHOLD_OPERATOR_EQ:
                return val.compareTo(target) == 0;
            default:
                throw new IllegalArgumentException("Unsupported operator: " + op);
        }
    }

    /**
     * 判断是否应该执行监控规则
     *
     * @param ruleDTO
     * @return
     */
    private Boolean shouldExecute(MetricsRuleDTO ruleDTO) {
        Integer intervalSecond = ruleDTO.getScanIntervalSecond();
        Date lastCheckedTime = ruleDTO.getLastCheckedTime();
        if (lastCheckedTime == null) {
            return true;
        }
        long now = System.currentTimeMillis() / 1000;
        long last = lastCheckedTime.getTime() / 1000;
        return (now - last) >= intervalSecond;
    }

    /**
     * 判断是否应该发送报警通知
     *
     * @param alarmDTO
     * @return
     */
    private Boolean shouldNotify(MetricsAlarmDTO alarmDTO) {
        Integer alarmIntervalSecond = alarmDTO.getAlarmIntervalSecond();
        Date lastAlarmTime = alarmDTO.getLastAlarmTime();
        if (lastAlarmTime == null) {
            return true;
        }
        long now = System.currentTimeMillis() / 1000;
        long last = lastAlarmTime.getTime() / 1000;
        return (now - last) >= alarmIntervalSecond;
    }

}

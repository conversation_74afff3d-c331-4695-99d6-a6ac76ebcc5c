package com.mega.platform.cloud;

import com.mega.platform.cloud.core.utils.SpringUtils;
import lombok.Getter;

@Getter
public enum MonitorErrorCode {

    METRICS_COLLECT_FAILED_CLIENT_IP_NOT_FOUND(20000),
    METRICS_COLLECT_FAILED_SERVICES_ID_NOT_FOUND(20001),
    ;

    private final Integer code;

    MonitorErrorCode(Integer code) {
        this.code = code;
    }

    public static MonitorErrorCode getExchangeCode(Integer code) {
        for (MonitorErrorCode exchangeCode : MonitorErrorCode.values()) {
            if (exchangeCode.getCode().equals(code)) {
                return exchangeCode;
            }
        }
        return null;
    }

    public String getMessage() {
        return SpringUtils.getLocaleMessage(this.code);
    }
}

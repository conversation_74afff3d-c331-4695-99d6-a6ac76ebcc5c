package com.mega.platform.cloud.monitor.service;

import com.mega.platform.cloud.MonitorException;
import com.mega.platform.cloud.common.mapper.EcsServerMapper;
import com.mega.platform.cloud.common.mapper.ServicesMapper;
import com.mega.platform.cloud.common.utils.StringUtils;
import com.mega.platform.cloud.core.utils.JsonUtils;
import com.mega.platform.cloud.data.dto.monitor.MetricsCollectDTO;
import com.mega.platform.cloud.data.dto.monitor.MetricsDTO;
import com.mega.platform.cloud.data.entity.EcsServer;
import com.mega.platform.cloud.data.entity.MonitorMetrics;
import com.mega.platform.cloud.data.entity.Services;
import com.mega.platform.cloud.monitor.dao.MetricsDao;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.mega.platform.cloud.MonitorErrorCode.METRICS_COLLECT_FAILED_CLIENT_IP_NOT_FOUND;
import static com.mega.platform.cloud.MonitorErrorCode.METRICS_COLLECT_FAILED_SERVICES_ID_NOT_FOUND;
import static com.mega.platform.cloud.common.constant.CommonConstant.BYTE_0;
import static com.mega.platform.cloud.common.constant.MonitorConstants.METRICS_SOURCE_TYPE_ECS;
import static com.mega.platform.cloud.common.constant.MonitorConstants.METRICS_SOURCE_TYPE_SERVICES;

@Slf4j
@Service
public class MetricsCollectService {

    private final MonitorCommonService monitorCommonService;
    private final MetricsDao metricsDao;
    private final EcsServerMapper ecsServerMapper;
    private final ServicesMapper servicesMapper;
    private final KafkaTemplate kafkaTemplate;
    private final MongoTemplate mongoTemplate;

    @Autowired
    public MetricsCollectService(MonitorCommonService monitorCommonService, MetricsDao metricsDao, EcsServerMapper ecsServerMapper, ServicesMapper servicesMapper, KafkaTemplate kafkaTemplate, MongoTemplate mongoTemplate) {
        this.monitorCommonService = monitorCommonService;
        this.metricsDao = metricsDao;
        this.ecsServerMapper = ecsServerMapper;
        this.servicesMapper = servicesMapper;
        this.kafkaTemplate = kafkaTemplate;
        this.mongoTemplate = mongoTemplate;
    }

    /**
     * 指标上传
     * ecs指标sourceId从requestIp获取
     * 服务指标sourceId=servicesId
     *
     * @param dto
     * @throws Exception
     */
    public void metricsCollect(MetricsCollectDTO dto) throws Exception {
        Integer sourceType = dto.getSourceType();
        if (sourceType.equals(METRICS_SOURCE_TYPE_ECS)) {
            EcsServer ecsServer = ecsServerMapper.selectOne(new EcsServer().setPrivateIp(dto.getClientIp()).setDelsign(BYTE_0));
            if (ecsServer == null) {
                log.warn("metricsCollect failed. clientIp {} not found.", dto.getClientIp());
                throw new MonitorException(METRICS_COLLECT_FAILED_CLIENT_IP_NOT_FOUND);
            }
            dto.setSourceId(ecsServer.getId());
        } else if (sourceType.equals(METRICS_SOURCE_TYPE_SERVICES)) {
            Services services = servicesMapper.selectByPrimaryKey(dto.getServicesId());
            if (services == null) {
                log.warn("metricsCollect failed. servicesId {} not found.", dto.getServicesId());
                throw new MonitorException(METRICS_COLLECT_FAILED_SERVICES_ID_NOT_FOUND);
            }
            dto.setSourceId(dto.getServicesId());
        }
        String topicName = monitorCommonService.getMetricsCollectTopic(dto.getSourceType());
        if (StringUtils.isNotEmpty(topicName)) {
            kafkaTemplate.send(topicName, JsonUtils.toJson(dto));
        }
    }

    /**
     * 服务器指标队列消费
     */
    @KafkaListener(topics = "${monitor.topic.metrics-ecs-collect}", groupId = "monitor")
    public void metricsEcsCollectKafkaListener(String message) throws Exception {
        MetricsCollectDTO dto = JsonUtils.fromJson(message, MetricsCollectDTO.class);
        log.info("metricsEcsCollectKafkaListener new message: {}", message);
        List<MetricsDTO> metrics = dto.getMetrics();
        if (metrics == null || metrics.isEmpty()) {
            log.info("metricsEcsCollectKafkaListener empty message");
            return;
        }
        Map<Integer, Map<String, Object>> metricsData = getMetricsData(dto);
        metricsData.forEach((metricsType, map) -> {
            String collectionName = monitorCommonService.getMetricsDataCollectionName(METRICS_SOURCE_TYPE_ECS, metricsType);
            if (StringUtils.isNotEmpty(collectionName)) {
                mongoTemplate.insert(map, collectionName);
            }
        });
    }

    /**
     * 服务指标队列消费
     */
    @KafkaListener(topics = "${monitor.topic.metrics-services-collect}", groupId = "monitor")
    public void metricsServicesCollectKafkaListener(String message) throws Exception {
        MetricsCollectDTO dto = JsonUtils.fromJson(message, MetricsCollectDTO.class);
        log.info("metricsServicesCollectKafkaListener new message: {}", message);
        List<MetricsDTO> metrics = dto.getMetrics();
        if (metrics == null || metrics.isEmpty()) {
            log.info("metricsServicesCollectKafkaListener empty message");
            return;
        }
        Map<Integer, Map<String, Object>> metricsData = getMetricsData(dto);
        metricsData.forEach((metricsType, map) -> {
            String collectionName = monitorCommonService.getMetricsDataCollectionName(METRICS_SOURCE_TYPE_SERVICES, metricsType);
            if (StringUtils.isNotEmpty(collectionName)) {
                mongoTemplate.insert(map, collectionName);
            }
        });
    }

    /**
     * 展平成mongo的map
     *
     * @param dto
     * @return
     */
    private Map<Integer, Map<String, Object>> getMetricsData(MetricsCollectDTO dto) {
        Map<Integer, Map<String, Object>> metricsDataMap = new HashMap<>();
        List<MetricsDTO> metrics = dto.getMetrics();
        List<Long> metricsIds = metrics.stream().map(MetricsDTO::getMetricsId).collect(Collectors.toList());
        Map<Long, MonitorMetrics> metricsMap = metricsDao.getMonitorMetricsMapByIds(metricsIds);
        metrics.forEach(metricsDTO -> {
            MonitorMetrics monitorMetrics = metricsMap.get(metricsDTO.getMetricsId());
            if (monitorMetrics != null) {
                metricsDataMap.computeIfAbsent(monitorMetrics.getType(), k -> new HashMap<>()).put(monitorMetrics.getKey(), metricsDTO.getValue());
            }
        });
        metricsDataMap.forEach((key, map) -> {
            map.put("sourceId", dto.getSourceId());
            map.put("collectTime", dto.getCollectTime());
        });
        return metricsDataMap;
    }

}

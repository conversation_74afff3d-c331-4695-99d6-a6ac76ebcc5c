package com.mega.platform.cloud.admin.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;

/**
 * 权限管理-角色删除请求参数
 */
@Data
@Accessors(chain = true)
@ApiModel("权限管理-角色删除请求参数")
public class AdminAccessRoleDeleteReqVO {

    @ApiModelProperty(value = "角色ID", example = "1")
    @NotNull(message = "角色ID不能为空")
    private Long adminRoleId;

}
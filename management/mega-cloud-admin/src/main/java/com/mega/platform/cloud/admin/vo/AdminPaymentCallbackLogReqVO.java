package com.mega.platform.cloud.admin.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel("订阅充值分页查询参数")
public class AdminPaymentCallbackLogReqVO {
    @ApiModelProperty("订单号")
    private Long orderNo;

    @ApiModelProperty("三方平台编码")
    private String platformCode;

    @ApiModelProperty("页码，从1开始")
    private Integer pageNum = 1;

    @ApiModelProperty("每页数量，默认50")
    private Integer pageSize = 50;
}

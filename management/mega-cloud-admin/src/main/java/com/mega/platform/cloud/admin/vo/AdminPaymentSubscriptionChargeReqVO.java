package com.mega.platform.cloud.admin.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel("订阅充值分页查询参数")
public class AdminPaymentSubscriptionChargeReqVO {
    @ApiModelProperty("订单号")
    private Long orderNo;

    @ApiModelProperty("订阅表id")
    private Long paymentSubscriptionId;

    @ApiModelProperty("页码，从1开始")
    private Integer pageNum = 1;

    @ApiModelProperty("每页数量，默认50")
    private Integer pageSize = 50;
}

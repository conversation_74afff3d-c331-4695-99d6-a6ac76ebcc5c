package com.mega.platform.cloud.admin.dao;

import com.mega.platform.cloud.data.entity.Project;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;

/**
 * 项目管理数据访问层
 */
@Repository
public interface AdminProjectDao extends Mapper<Project> {
    
    /**
     * 分页查询项目列表
     * @param offset 偏移量
     * @param pageSize 页大小
     * @param name 项目名称（模糊查询）
     * @param status 状态
     * @return 项目列表
     */
    List<Project> selectProjectList(@Param("offset") Integer offset, 
                                   @Param("pageSize") Integer pageSize,
                                   @Param("name") String name,
                                   @Param("status") Integer status);
    
    /**
     * 查询项目总数
     * @param name 项目名称（模糊查询）
     * @param status 状态
     * @return 总数
     */
    Long countProjectList(@Param("name") String name, 
                         @Param("status") Integer status);
    
    /**
     * 根据ID查询项目（排除已删除）
     * @param id 项目ID
     * @return 项目信息
     */
    Project selectProjectById(@Param("id") Long id);
    
    /**
     * 根据名称查询项目（排除已删除）
     * @param name 项目名称
     * @param excludeId 排除的项目ID
     * @return 项目信息
     */
    Project selectProjectByName(@Param("name") String name, 
                               @Param("excludeId") Long excludeId);
    
    /**
     * 逻辑删除项目
     * @param id 项目ID
     * @return 影响行数
     */
    int deleteProjectById(@Param("id") Long id);
}
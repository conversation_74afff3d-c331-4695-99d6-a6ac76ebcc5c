package com.mega.platform.cloud.admin.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * 权限管理-路由列表响应参数
 */
@Data
@Accessors(chain = true)
@ApiModel("权限管理-路由列表响应参数")
public class AdminAccessRouterListRespVO {

    @ApiModelProperty(value = "路由ID", example = "1")
    private Long id;

    @ApiModelProperty(value = "后端路由路径", example = "/admin/api/system/user/list")
    private String backendPath;

    @ApiModelProperty(value = "前端路由路径", example = "/dashboard")
    private String frontendPath;

    @ApiModelProperty(value = "前端路由名称", example = "Dashboard")
    private String frontendName;

    @ApiModelProperty(value = "路由描述", example = "仪表板")
    private String description;

    @ApiModelProperty(value = "父路由ID", example = "0")
    private Long parentAdminRouterId;

    @ApiModelProperty(value = "创建时间", example = "2024-01-01 12:00:00")
    private Date createTime;

    @ApiModelProperty(value = "更新时间", example = "2024-01-01 12:00:00")
    private Date updateTime;

    @ApiModelProperty(value = "删除标识", example = "0")
    private Integer delsign;
}
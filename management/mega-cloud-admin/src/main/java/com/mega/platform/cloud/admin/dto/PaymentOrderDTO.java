package com.mega.platform.cloud.admin.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
@Data
public class PaymentOrderDTO {
    private Long id;
    private Long paymentProductConfigId;
    private Long parentPaymentOrderId;

    private String orderNo;
    private String outOrderNo;

    private BigDecimal totalAmount;
    private String currency;

    private Integer status; // 0=初始化, 1=成功, 2=失败, 3=已退款
    private Integer sandBox; // 1=是，0=否

    private LocalDateTime payTime;

    private String extra; // JSON 字符串

    private LocalDateTime createTime;
    private LocalDateTime updateTime;

    private Integer delsign;

    // 以下是联表字段（来自 payment_product_config 表）
    private String platformCode; // 如：APPLE, WECHAT, ALIPAY
}

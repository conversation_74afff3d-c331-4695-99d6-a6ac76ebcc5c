package com.mega.platform.cloud.admin.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * 管理员用户详细信息响应数据
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@ApiModel("管理员用户详细信息响应数据")
public class AdminAuthProfileRespVO {

    @ApiModelProperty(value = "管理员用户ID", example = "1")
    private Long adminUserId;

    @ApiModelProperty(value = "管理员用户名", example = "admin")
    private String adminUserName;

    @ApiModelProperty(value = "管理员角色列表")
    private List<AdminRoleVO> roles;

    @ApiModelProperty(value = "管理员拥有的项目列表")
    private List<AdminProjectVO> projects;

    @ApiModelProperty(value = "管理员有用的路由列表")
    private List<AdminFirstRouterVO> routers;


    @Data
    @Accessors(chain = true)
    @ApiModel("管理员角色数据")
    public static class AdminRoleVO {
        @ApiModelProperty(value = "角色ID", example = "1")
        private Long id; // 角色ID
        @ApiModelProperty(value = "角色名称", example = "管理员")
        private String name; // 角色名称  
        @ApiModelProperty(value = "角色描述", example = "管理员角色")
        private String description; // 角色描述
    }


    @Data
    @Accessors(chain = true)
    @ApiModel("管理员一级路由数据")
    public static class AdminFirstRouterVO {
        private Long id; 

        @ApiModelProperty(value = "前端路由路径", example = "/dashboard")
        private String frontendPath; // 前端路由路径

        @ApiModelProperty(value = "前端路由名称", example = "系统仪表盘")
        private String frontendName; // 前端路由名称

        @ApiModelProperty(value = "子路由列表")
        private List<AdminSecondRouterVO> childRouters;

        // @ApiModelProperty(value = "路由描述", example = "系统仪表盘")
        // private String description; // 路由描述
    }

    @Data
    @Accessors(chain = true)
    @ApiModel("管理员子路由数据")
    public static class AdminSecondRouterVO {
        private Long id; // routerID

        @ApiModelProperty(value = "前端路由路径", example = "/dashboard")
        private String frontendPath; // 前端路由路径

        @ApiModelProperty(value = "前端路由名称", example = "系统仪表盘")
        private String frontendName; // 前端路由名称

        @ApiModelProperty(value = "子路由列表")
        private List<AdminThirdRouterVO> childRouters;
    }

    @Data
    @Accessors(chain = true)
    @ApiModel("管理员子路由数据")
    public static class AdminThirdRouterVO {
        private Long id; // routerID
        
        @ApiModelProperty(value = "前端路由路径", example = "/dashboard")
        private String frontendPath; // 前端路由路径

        @ApiModelProperty(value = "前端路由名称", example = "系统仪表盘")
        private String frontendName; // 前端路由名称
    }
    

    @Data
    @Accessors(chain = true)
    @ApiModel("管理员项目数据")
    public static class AdminProjectVO {
        @ApiModelProperty(value = "项目id", example = "1")
        private Long id; // 项目ID

        @ApiModelProperty(value = "项目名称", example = "项目名称")
        private String name;
    }

}
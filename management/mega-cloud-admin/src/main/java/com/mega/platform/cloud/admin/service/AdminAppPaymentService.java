package com.mega.platform.cloud.admin.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.mega.platform.cloud.AdminErrorCode;
import com.mega.platform.cloud.AdminException;
import com.mega.platform.cloud.admin.dao.AdminAppDao;
import com.mega.platform.cloud.admin.dto.PaymentOrderDTO;
import com.mega.platform.cloud.admin.vo.*;
import com.mega.platform.cloud.common.enums.ThirdPlatformEnum;
import com.mega.platform.cloud.common.mapper.PaymentAppConfigMapper;
import com.mega.platform.cloud.common.mapper.PaymentProductConfigMapper;
import com.mega.platform.cloud.core.PageResult;
import com.mega.platform.cloud.data.entity.PaymentAppConfig;
import com.mega.platform.cloud.data.entity.PaymentCallbackLog;
import com.mega.platform.cloud.data.entity.PaymentProductConfig;
import com.mega.platform.cloud.data.entity.PaymentSubscriptionChargeLog;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
@Slf4j
@RequiredArgsConstructor
public class AdminAppPaymentService {
    private final PaymentAppConfigMapper paymentAppConfigMapper;
    private final PaymentProductConfigMapper paymentProductConfigMapper;
    private final AdminAppDao adminAppDao;
    public void paymentConfigCreate(Long projectId, AdminAppPaymentConfigReqVO reqVO) throws JsonProcessingException {
        Long thirdPlatformId = reqVO.getThirdPlatformId();
        String config;
        ObjectMapper objectMapper = new ObjectMapper();
        switch (ThirdPlatformEnum.fromCode(thirdPlatformId)) {
            case WECHAT:
                config = objectMapper.writeValueAsString(reqVO.getPaymentWechatConfig());
                break;
            case APPLE:
                config = objectMapper.writeValueAsString(reqVO.getPaymentAppleConfig());
                break;
            case ALIPAY:
                config = objectMapper.writeValueAsString(reqVO.getPaymentAlipayConfig());
                break;
            default:
                throw new AdminException(AdminErrorCode.ERR_0);
        }
        PaymentAppConfig paymentAppConfig = new PaymentAppConfig().setProjectAppId(projectId).setConfig(config).setThirdPlatformId(thirdPlatformId);
        paymentAppConfigMapper.insertSelective(paymentAppConfig);
    }

    public void paymentConfigDelete(Long appId, Long thirdPlatformId) {
        paymentAppConfigMapper.delete(new PaymentAppConfig().setProjectAppId(appId).setThirdPlatformId(thirdPlatformId));
    }

    public void paymentConfigUpdate(Long projectId, AdminAppPaymentConfigReqVO reqVO) throws JsonProcessingException {
        Long thirdPlatformId = reqVO.getThirdPlatformId();
        String config;
        ObjectMapper objectMapper = new ObjectMapper();
        switch (ThirdPlatformEnum.fromCode(thirdPlatformId)) {
            case WECHAT:
                config = objectMapper.writeValueAsString(reqVO.getPaymentWechatConfig());
                break;
            case APPLE:
                config = objectMapper.writeValueAsString(reqVO.getPaymentAppleConfig());
                break;
            case ALIPAY:
                config = objectMapper.writeValueAsString(reqVO.getPaymentAlipayConfig());
                break;
            default:
                throw new AdminException(AdminErrorCode.ERR_0);
        }

        PaymentAppConfig update = new PaymentAppConfig()
                .setProjectAppId(projectId)
                .setThirdPlatformId(thirdPlatformId)
                .setConfig(config);

        paymentAppConfigMapper.updateByPrimaryKeySelective(update);
    }

    public List<PaymentAppConfig> paymentConfigList(AdminAppPaymentHandleConfigReqVO reqVO) {
        return paymentAppConfigMapper.select(new PaymentAppConfig().setProjectAppId(reqVO.getProjectAppId()).setThirdPlatformId(reqVO.getThirdPlatformId()));
    }

    public void paymentProductConfigCreate(AdminAppProductConfigReqVO reqVO) {
        PaymentProductConfig paymentProductConfig = new PaymentProductConfig();
        BeanUtils.copyProperties(reqVO, paymentProductConfig);
        paymentProductConfigMapper.insertSelective(paymentProductConfig);
    }

    public void paymentProductConfigDelete(Long productId) {
        PaymentProductConfig paymentProductConfig = paymentProductConfigMapper.selectByPrimaryKey(productId);
        paymentProductConfig.setDelsign((byte) 1);
        paymentProductConfigMapper.updateByPrimaryKeySelective(paymentProductConfig);
    }

    public void paymentProductConfigUpdate(AdminAppProductConfigUpdateReqVO reqVO) {
        PaymentProductConfig paymentProductConfig = paymentProductConfigMapper.selectByPrimaryKey(reqVO.getProductId());
        BeanUtils.copyProperties(reqVO, paymentProductConfig);
        paymentProductConfigMapper.updateByPrimaryKeySelective(paymentProductConfig);

    }

    public List<PaymentProductConfig> paymentProductConfigList(AdminAppPaymentProductConfigListReqVO reqVO) {
        return paymentProductConfigMapper.select(new PaymentProductConfig().setProjectAppId(reqVO.getProjectAppId()).setThirdPlatformId(reqVO.getThirdPlatformId()).setDelsign((byte) 0));
    }

    public PageResult<AdminPaymentOrderRespVO> findOrderList(AdminPaymentOrderListReqVO reqVO) {
        if ((reqVO.getOrderId() == null && reqVO.getParentId() == null) || (reqVO.getOrderId() != null && reqVO.getParentId() != null)) {
            throw new  AdminException(AdminErrorCode.ERR_0);
        }
        int pageNum = Optional.ofNullable(reqVO.getPageNum()).orElse(1);
        int pageSize = Optional.ofNullable(reqVO.getPageSize()).orElse(50);
        int offset = (pageNum - 1) * pageSize;
        Long parentId = adminAppDao.selectParentId(reqVO.getOrderId());
        reqVO.setParentId(parentId);
        Long total = adminAppDao.countOrderList(reqVO);
        if (total == 0L) {
            PageResult<AdminPaymentOrderRespVO> result = new PageResult<>();
            result.setTotal(0L);
            result.setList(null);
            return result;
        }

        List<PaymentOrderDTO> orderList = adminAppDao.selectOrderList(reqVO, offset, pageSize);
        List<AdminPaymentOrderRespVO> voList = orderList.stream()
                .map(this::convertToRespVO)
                .collect(Collectors.toList());
        PageResult<AdminPaymentOrderRespVO> result = new PageResult<>();
        result.setTotal(total);
        result.setList(voList);
        return result;
    }

    private AdminPaymentOrderRespVO convertToRespVO(PaymentOrderDTO po) {
        AdminPaymentOrderRespVO vo = new AdminPaymentOrderRespVO();
        BeanUtils.copyProperties(po, vo);
        return vo;
    }

    public PageResult<AdminPaymentSubscriptionRespVO> querySubscriptionPage(AdminPaymentSubscriptionListReqVO reqVO) {
        Integer pageNum = Optional.ofNullable(reqVO.getPageNum()).orElse(1);
        Integer pageSize = Optional.ofNullable(reqVO.getPageSize()).orElse(50);
        int offset = (pageNum - 1) * pageSize;
        Long parentId = null;
        if (reqVO.getOrderId() != null) {
             parentId = adminAppDao.selectParentId(reqVO.getOrderId());
        }

        // 查询分页数据
        List<AdminPaymentSubscriptionRespVO> records = adminAppDao.selectSubscriptionPage(reqVO, parentId, offset, pageSize);
        Long total = adminAppDao.selectSubscriptionCount(reqVO, parentId);

        PageResult<AdminPaymentSubscriptionRespVO> result = new PageResult<>();
        result.setTotal(total);
        result.setList(records);
        return result;
    }

    public PageResult<PaymentSubscriptionChargeLog> querySubscriptionChargeLogPage(AdminPaymentSubscriptionChargeReqVO reqVO) {
        Integer pageNum = Optional.ofNullable(reqVO.getPageNum()).orElse(1);
        Integer pageSize = Optional.ofNullable(reqVO.getPageSize()).orElse(50);
        int offset = (pageNum - 1) * pageSize;
        Long parentId = null;
        if (reqVO.getOrderNo() != null) {
            parentId = adminAppDao.selectParentId(reqVO.getOrderNo());
        }

        // 查询分页数据
        List<PaymentSubscriptionChargeLog> records = adminAppDao.selectSubscriptionChargeLogPage(reqVO, parentId, offset, pageSize);
        Long total = adminAppDao.selectSubscriptionChargeLogCount(reqVO, parentId);

        PageResult<PaymentSubscriptionChargeLog> result = new PageResult<>();
        result.setTotal(total);
        result.setList(records);
        return result;
    }

    public PageResult<PaymentCallbackLog> queryPaymentCallbackLogPage(AdminPaymentCallbackLogReqVO reqVO) {
        Integer pageNum = Optional.ofNullable(reqVO.getPageNum()).orElse(1);
        Integer pageSize = Optional.ofNullable(reqVO.getPageSize()).orElse(50);
        int offset = (pageNum - 1) * pageSize;
        Long parentId = null;
        if (reqVO.getOrderNo() != null) {
            parentId = adminAppDao.selectParentId(reqVO.getOrderNo());
        }

        // 查询分页数据
        List<PaymentCallbackLog> records = adminAppDao.selectPaymentCallbackLogPage(reqVO, parentId, offset, pageSize);
        Long total = adminAppDao.selectPaymentCallbackLoCount(reqVO, parentId);

        PageResult<PaymentCallbackLog> result = new PageResult<>();
        result.setTotal(total);
        result.setList(records);
        return result;
    }
}

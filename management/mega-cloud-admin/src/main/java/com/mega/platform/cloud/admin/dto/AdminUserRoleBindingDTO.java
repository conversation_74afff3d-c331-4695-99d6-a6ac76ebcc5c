package com.mega.platform.cloud.admin.dto;

import javax.persistence.Column;

import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class AdminUserRoleBindingDTO {
     /**
     * 管理员ID
     */
    @Column(name = "admin_user_id")
    private Long adminUserId;

    /**
     * 角色ID
     */
    @Column(name = "admin_role_id")
    private Long adminRoleId;


    @Column(name = "admin_user_name")
    private String adminUserName;

    
    @Column(name = "delsign")
    private Boolean delsign;
}

package com.mega.platform.cloud.admin.util;

import java.util.Date;

import com.mega.platform.cloud.admin.constant.AdminAuthConstant;

import io.jsonwebtoken.Claims;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.SignatureAlgorithm;
import io.jsonwebtoken.security.Keys;

import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.security.Key;

public class AdminCryptUtils {
    
    /**
     * 生成简化的JWT Token（只包含adminUserId和tokenVersion）
     */
    public static String generateSimpleJwtToken(Long adminUserId, Long tokenVersion) {
        Date now = new Date();
        Date expiration = new Date(now.getTime() + AdminAuthConstant.JWT_EXPIRATION_TIME * 1000);

        return Jwts.builder()
                .claim(AdminAuthConstant.CONTEXT_ADMIN_USER_ID, adminUserId)
                .claim(AdminAuthConstant.CONTEXT_TOKEN_VERSION, tokenVersion)
                .setIssuedAt(now)
                .setExpiration(expiration)
                .signWith(getSigningKey(), SignatureAlgorithm.HS256)
                .compact();
    }

    /**
     * 获取签名密钥
     */
    private static Key getSigningKey() {
        byte[] keyBytes = AdminAuthConstant.JWT_SECRET.getBytes(StandardCharsets.UTF_8);
        return Keys.hmacShaKeyFor(keyBytes);
    }

        /**
     * 解析JWT Token
     */
    public static  Claims parseJwtToken(String token) {
        return Jwts.parserBuilder()
                .setSigningKey(getSigningKey())
                .build()
                .parseClaimsJws(token)
                .getBody();
    }
}

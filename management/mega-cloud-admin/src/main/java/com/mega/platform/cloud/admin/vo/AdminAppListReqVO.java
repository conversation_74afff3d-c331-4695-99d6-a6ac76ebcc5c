package com.mega.platform.cloud.admin.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@ApiModel("应用列表查询请求参数")
public class AdminAppListReqVO {
    
    @ApiModelProperty(value = "页码", example = "1")
    private Integer pageNum = 1;
    
    @ApiModelProperty(value = "页大小", example = "20") 
    private Integer pageSize = 20;
    
    @ApiModelProperty(value = "应用名称", example = " ")
    private String name;
    
    @ApiModelProperty(value = "状态：-1全部 0不可用，1正常，2挂起，3审核中", example = "-1")
    private Integer status;
}
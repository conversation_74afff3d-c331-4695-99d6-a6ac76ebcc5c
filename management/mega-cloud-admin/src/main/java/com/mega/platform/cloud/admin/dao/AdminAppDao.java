package com.mega.platform.cloud.admin.dao;

import com.mega.platform.cloud.admin.dto.PaymentOrderDTO;
import com.mega.platform.cloud.admin.vo.*;
import com.mega.platform.cloud.data.entity.PaymentCallbackLog;
import com.mega.platform.cloud.data.entity.PaymentSubscriptionChargeLog;
import com.mega.platform.cloud.data.entity.ProjectApp;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;

/**
 * 应用管理数据访问层
 */
@Repository
public interface AdminAppDao extends Mapper<ProjectApp> {
    
    /**
     * 分页查询应用列表
     * @param projectId 项目ID
     * @param offset 偏移量
     * @param pageSize 页大小
     * @param name 应用名称
     * @param status 状态
     * @return 应用列表
     */
    List<ProjectApp> selectAppList(@Param("projectId") Long projectId,
                                   @Param("offset") Integer offset,
                                   @Param("pageSize") Integer pageSize,
                                   @Param("name") String name,
                                   @Param("status") Integer status);
    
    /**
     * 查询应用总数
     * @param projectId 项目ID
     * @param name 应用名称
     * @param status 状态
     * @return 总数
     */
    Long countAppList(@Param("projectId") Long projectId,
                          @Param("name") String name,
                          @Param("status") Integer status);
    
    /**
     * 根据项目ID和应用ID查询应用详情
     * @param projectId 项目ID
     * @param id 应用ID
     * @return 应用详情
     */
    ProjectApp selectAppByProjectIdAndId(@Param("projectId") Long projectId,
                                         @Param("id") Long id);
    
    /**
     * 根据项目ID和应用名称查询应用
     * @param projectId 项目ID
     * @param name 应用名称
     * @param excludeId 排除的应用ID（编辑时使用）
     * @return 应用信息
     */
    ProjectApp selectAppByProjectIdAndName(@Param("projectId") Long projectId,
                                           @Param("name") String name,
                                           @Param("excludeId") Long excludeId);

    Long selectParentId(@Param("orderId") Long orderId);
    Long countOrderList(@Param("req") AdminPaymentOrderListReqVO reqVO);

    List<PaymentOrderDTO> selectOrderList(@Param("req") AdminPaymentOrderListReqVO reqVO,
                                          @Param("offset") int offset,
                                          @Param("limit") int limit);

    List<AdminPaymentSubscriptionRespVO> selectSubscriptionPage(@Param("req") AdminPaymentSubscriptionListReqVO reqVO, @Param("parentId") Long parentId, @Param("offset") int offset, @Param("limit") Integer pageSize);

    Long selectSubscriptionCount(@Param("req") AdminPaymentSubscriptionListReqVO reqVO, @Param("parentId") Long parentId);

    List<PaymentSubscriptionChargeLog> selectSubscriptionChargeLogPage(@Param("req") AdminPaymentSubscriptionChargeReqVO reqVO, @Param("parentId") Long parentId, @Param("offset") int offset, @Param("limit") Integer pageSize);

    Long selectSubscriptionChargeLogCount(@Param("req") AdminPaymentSubscriptionChargeReqVO reqVO, @Param("parentId") Long parentId);

    List<PaymentCallbackLog> selectPaymentCallbackLogPage(@Param("req") AdminPaymentCallbackLogReqVO reqVO, @Param("parentId") Long parentId, @Param("offset") int offset, @Param("limit")  Integer pageSize);

    Long selectPaymentCallbackLoCount(@Param("req") AdminPaymentCallbackLogReqVO reqVO, @Param("parentId") Long parentId);
}
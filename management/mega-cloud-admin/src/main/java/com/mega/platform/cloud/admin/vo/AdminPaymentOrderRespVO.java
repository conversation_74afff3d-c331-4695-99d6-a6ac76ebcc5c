package com.mega.platform.cloud.admin.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
@Data
@ApiModel("支付订单响应")
public class AdminPaymentOrderRespVO {

    @ApiModelProperty(value = "订单主键ID", example = "10001")
    private Long id;

    @ApiModelProperty(value = "支付平台订单号", example = "ALI202507221234567890")
    private String orderNo;

    @ApiModelProperty(value = "外部订单号（中台生成）", example = "OUT202507221111")
    private String outOrderNo;

    @ApiModelProperty(value = "订单总金额", example = "68.00")
    private BigDecimal totalAmount;

    @ApiModelProperty(value = "币种", example = "CNY")
    private String currency;

    @ApiModelProperty(value = "订单状态（0=初始化, 1=成功, 2=失败, 3=已退款）", example = "1")
    private Integer status;

    @ApiModelProperty(value = "支付完成时间", example = "2025-07-22T14:30:00")
    private LocalDateTime payTime;

    @ApiModelProperty(value = "支付平台代码", example = "APPLE")
    private String platformCode;

    @ApiModelProperty(value = "订单类型", example = "SUBSCRIPTION")
    private String orderType;
}

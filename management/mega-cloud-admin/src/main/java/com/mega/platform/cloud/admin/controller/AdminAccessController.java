package com.mega.platform.cloud.admin.controller;

import com.mega.platform.cloud.admin.service.AdminAccessService;
import com.mega.platform.cloud.admin.vo.*;
import com.mega.platform.cloud.core.PageResult;
import com.mega.platform.cloud.core.Result;
import com.mega.platform.cloud.core.Results;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 权限管理控制器
 */
@RestController
@RequestMapping("/admin/api")
@Api(tags = "权限管理")
@Slf4j
public class AdminAccessController {

    private final AdminAccessService adminAccessService;

    @Autowired
    public AdminAccessController(AdminAccessService adminAccessService) {
        this.adminAccessService = adminAccessService;
    }

    // ==================== 用户管理 ====================
    
    @PostMapping("/system/access/user/list")
    @ApiOperation("权限管理-管理员列表")
    public Result<List<AdminAccessUserListRespVO>> getUserList(@Validated @RequestBody AdminAccessUserListReqVO reqVO) {
        //OK
        List<AdminAccessUserListRespVO> result = adminAccessService.getUserList(reqVO);
        return Results.success(result);
    }
    
    @PostMapping("/system/access/user/create")
    @ApiOperation("权限管理-创建管理员")
    public Result<?> createUser(@Validated @RequestBody AdminAccessUserCreateReqVO reqVO) {
        //ok
        adminAccessService.createUser(reqVO);
        return Results.success();
    }
    
    @PostMapping("/system/access/user/delete")
    @ApiOperation("权限管理-删除管理员")
    public Result<?> deleteUser(@Validated @RequestBody AdminAccessUserDeleteReqVO reqVO) {
        // ok
        adminAccessService.deleteUser(reqVO);
        return Results.success();
    }
    
    @PostMapping("/system/access/user/reset-password")
    @ApiOperation("权限管理-重置管理员密码")
    public Result<?> resetUserPassword(@Validated @RequestBody AdminAccessUserResetPasswordReqVO reqVO) {
        // ok 
        adminAccessService.resetUserPassword(reqVO);
        return Results.success();
    }

    // ==================== 用户项目绑定管理 ====================
    
    @PostMapping("/system/access/user-project/binding/list")
    @ApiOperation("权限管理-管理员项目绑定列表")
    public Result<List<AdminAccessUserProjectBindingListRespVO>> getUserProjectBindingList(
            @Validated @RequestBody AdminAccessUserProjectBindingListReqVO reqVO) {
        // ok
        List<AdminAccessUserProjectBindingListRespVO> result = adminAccessService.getUserProjectBindingList(reqVO);
        return Results.success(result);
    }
    
    @PostMapping("/system/access/user-project/binding/update")
    @ApiOperation("权限管理-更新管理员项目绑定")
    public Result<?> updateUserProjectBinding(@Validated @RequestBody AdminAccessUserProjectBindingUpdateReqVO reqVO) {
        // ok
        adminAccessService.updateUserProjectBinding(reqVO);
        return Results.success();
    }

    // ==================== 用户路由绑定管理 ====================
    
    @PostMapping("/system/access/user-router/binding/list")
    @ApiOperation("权限管理-管理员路由绑定列表")
    public Result<List<AdminAccessUserRouterBindingListRespVO>> getUserRouterBindingList(
            @Validated @RequestBody AdminAccessUserRouterBindingListReqVO reqVO) {
        // ok 
        List<AdminAccessUserRouterBindingListRespVO> result = adminAccessService.getUserRouterBindingList(reqVO);
        return Results.success(result);
    }
    
    @PostMapping("/system/access/user-router/binding/update")
    @ApiOperation("权限管理-更新管理员路由绑定")
    public Result<?> updateUserRouterBinding(@Validated @RequestBody AdminAccessUserRouterBindingUpdateReqVO reqVO) {
        // ok 
        adminAccessService.updateUserRouterBinding(reqVO);
        return Results.success();
    }

    // ==================== 角色管理 ====================
    
    @PostMapping("/system/access/role/list")
    @ApiOperation("权限管理-角色列表")
    public Result<List<AdminAccessRoleListRespVO>> getRoleList(@Validated @RequestBody AdminAccessRoleListReqVO reqVO) {
        // ok
        List<AdminAccessRoleListRespVO> result = adminAccessService.getRoleList(reqVO);
        return Results.success(result);
    }
    
    @PostMapping("/system/access/role/create")
    @ApiOperation("权限管理-创建角色")
    public Result<?> createRole(@Validated @RequestBody AdminAccessRoleCreateReqVO reqVO) {
        // ok
        adminAccessService.createRole(reqVO);
        return Results.success();
    }
    
    @PostMapping("/system/access/role/delete")
    @ApiOperation("权限管理-删除角色")
    public Result<?> deleteRole(@Validated @RequestBody AdminAccessRoleDeleteReqVO reqVO) {
        // ok
        adminAccessService.deleteRole(reqVO);
        return Results.success();
    }

    // ==================== 角色路由绑定管理 ====================
    
    @PostMapping("/system/access/role-router/binding/list")
    @ApiOperation("权限管理-角色路由绑定列表")
    public Result<List<AdminAccessRoleRouterBindingListRespVO>> getRoleRouterBindingList(
            @Validated @RequestBody AdminAccessRoleRouterBindingListReqVO reqVO) {
        // ok 
        List<AdminAccessRoleRouterBindingListRespVO> result = adminAccessService.getRoleRouterBindingList(reqVO);
        return Results.success(result);
    }
    
    @PostMapping("/system/access/role-router/binding/update")
    @ApiOperation("权限管理-更新角色路由绑定")
    public Result<?> updateRoleRouterBinding(@Validated @RequestBody AdminAccessRoleRouterBindingUpdateReqVO reqVO) {
        // ok 
        adminAccessService.updateRoleRouterBinding(reqVO);
        return Results.success();
    }

    // ==================== 角色项目绑定管理 ====================
    
    @PostMapping("/system/access/role-project/binding/list")
    @ApiOperation("权限管理-角色项目绑定列表")
    public Result<List<AdminAccessRoleProjectBindingListRespVO>> getRoleProjectBindingList(
            @Validated @RequestBody AdminAccessRoleProjectBindingListReqVO reqVO) {
        // ok
        List<AdminAccessRoleProjectBindingListRespVO> result = adminAccessService.getRoleProjectBindingList(reqVO);
        return Results.success(result);
    }
    
    @PostMapping("/system/access/role-project/binding/update")
    @ApiOperation("权限管理-更新角色项目绑定")
    public Result<?> updateRoleProjectBinding(@Validated @RequestBody AdminAccessRoleProjectBindingUpdateReqVO reqVO) {
        // ok
        adminAccessService.updateRoleProjectBinding(reqVO);
        return Results.success();
    }

    // ==================== 角色用户绑定管理 ====================
    
    @PostMapping("/system/access/role-user/binding/list")
    @ApiOperation("权限管理-角色用户绑定列表")
    public Result<List<AdminAccessRoleUserBindingListRespVO>> getRoleUserBindingList(
            @Validated @RequestBody AdminAccessRoleUserBindingListReqVO reqVO) {
        // ok 
        List<AdminAccessRoleUserBindingListRespVO> result = adminAccessService.getRoleUserBindingList(reqVO);
        return Results.success(result);
    }
    
    @PostMapping("/system/access/role-user/binding/update")
    @ApiOperation("权限管理-更新角色用户绑定")
    public Result<?> updateRoleUserBinding(@Validated @RequestBody AdminAccessRoleUserBindingUpdateReqVO reqVO) {
        // ok 
        adminAccessService.updateRoleUserBinding(reqVO);
        return Results.success();
    }

    // ==================== 路由管理 ====================
    
    @PostMapping("/system/access/router/list")
    @ApiOperation("权限管理-路由列表")
    public Result<List<AdminAccessRouterListRespVO>> getRouterList(@Validated @RequestBody AdminAccessRouterListReqVO reqVO) {
        List<AdminAccessRouterListRespVO> result = adminAccessService.getRouterList(reqVO);
        return Results.success(result);
    }

}
package com.mega.platform.cloud.admin.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;
@Data
@ApiModel("支付订单分页查询请求")
public class AdminPaymentOrderListReqVO {

    @ApiModelProperty(value = "页码，从1开始", example = "1")
    private Integer pageNum;

    @ApiModelProperty(value = "每页大小，最大100", example = "20")
    private Integer pageSize;

    @ApiModelProperty(value = "订单号", example = "20250722XXX")
    private Long orderId;

    @ApiModelProperty(value = "父订单ID，查询其及其子订单", example = "10001")
    private Long parentId;

    @ApiModelProperty(value = "平台代码，例如 ALIPAY、WECHAT、APPLE", example = "ALIPAY")
    private String platformCode;

    @ApiModelProperty(value = "订单类型，例如 1订阅 0 内购", example = "0")
    private Boolean isSubscription;

    @ApiModelProperty(value = "订单状态（0=初始化, 1=成功, 2=失败, 3=已退款）", example = "1")
    private Integer status;

    @ApiModelProperty(value = "支付时间-起始", example = "2025-07-01T00:00:00")
    private LocalDateTime payTimeStart;

    @ApiModelProperty(value = "支付时间-结束", example = "2025-07-23T23:59:59")
    private LocalDateTime payTimeEnd;
}

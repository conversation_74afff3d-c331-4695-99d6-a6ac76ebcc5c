package com.mega.platform.cloud.admin.service;

import com.mega.platform.cloud.AdminException;
import com.mega.platform.cloud.admin.dao.AdminAccessDao;
import com.mega.platform.cloud.admin.dto.AdminRoleProjectBindingDTO;
import com.mega.platform.cloud.admin.dto.AdminRoleRouterBindingDTO;
import com.mega.platform.cloud.admin.dto.AdminUserProjectBindingDTO;
import com.mega.platform.cloud.admin.dto.AdminUserRoleBindingDTO;
import com.mega.platform.cloud.admin.vo.*;
import com.mega.platform.cloud.core.PageResult;
import com.mega.platform.cloud.core.utils.DateUtils;
import com.mega.platform.cloud.data.entity.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.time.ZoneId;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 权限管理服务类
 */
@Service
@Slf4j
public class AdminAccessService {

    private final AdminAccessDao adminAccessDao;
    private final AdminSuperAccessService adminSuperAccessService;
    private final AdminAccessCacheService adminAccessCacheService;

    @Autowired
    public AdminAccessService(AdminAccessDao adminAccessDao, AdminSuperAccessService adminSuperAccessService, AdminAccessCacheService adminAccessCacheService) {
        this.adminAccessDao = adminAccessDao;
        this.adminSuperAccessService = adminSuperAccessService;
        this.adminAccessCacheService = adminAccessCacheService;
    }

    // ==================== 用户管理 ====================

    /**
     * 查询管理员列表
     */
    public List<AdminAccessUserListRespVO> getUserList(AdminAccessUserListReqVO reqVO) {
        // 查询所有用户
        List<AdminUser> users = adminAccessDao.selectUserList(
                reqVO.getAdminUsername(), reqVO.getDelsign());

        // 转换VO
        List<AdminAccessUserListRespVO> respList = users.stream()
                .map(this::convertToUserListRespVO)
                .collect(Collectors.toList());

        return respList;
    }

    /**
     * 创建管理员
     */
    @Transactional
    public void createUser(AdminAccessUserCreateReqVO reqVO) {
        AdminUser adminUser = new AdminUser();
        adminUser.setUsername(reqVO.getAdminUsername());
        adminUser.setPassword(reqVO.getPassword()); // 注意：实际应用中需要加密

        int result = adminAccessDao.insertUser(adminUser);
        if (result <= 0) {
            throw new RuntimeException("创建管理员失败");
        }
    }

    /**
     * 删除管理员
     */
    @Transactional
    public void deleteUser(AdminAccessUserDeleteReqVO reqVO) {
        int result = adminAccessDao.deleteUser(reqVO.getAdminUserId());
        if (result <= 0) {
            throw new RuntimeException("删除管理员失败");
        }
    }

    /**
     * 重置管理员密码
     */
    @Transactional
    public void resetUserPassword(AdminAccessUserResetPasswordReqVO reqVO) {
        int result = adminAccessDao.updateUserPassword(reqVO.getAdminUserId(), reqVO.getNewPassword());
        if (result <= 0) {
            throw new RuntimeException("重置密码失败");
        }
    }

    // ==================== 用户项目绑定管理 ====================

    /**
     * 查询管理员项目绑定列表
     */
    public List<AdminAccessUserProjectBindingListRespVO> getUserProjectBindingList(
            AdminAccessUserProjectBindingListReqVO reqVO) {
        // 查询管理员绑定的项目列表
        List<AdminUserProjectBindingDTO> bindings = adminAccessDao.selectUserProjectBindingList(reqVO.getAdminUserId(),
                reqVO.getDelsign());

        // 如果是超管，需要合并项目绑定
        if (adminSuperAccessService.isSuperAdmin(reqVO.getAdminUserId())) {
            List<AdminUserProjectBindingDTO> superProjectBindings = adminSuperAccessService
                    .getSuperAdminProjectBindings(reqVO.getAdminUserId());
            bindings.addAll(superProjectBindings);
        }

        // 转换VO并按照adminUserId、projectId分组,取delsign值最大的记录
        List<AdminAccessUserProjectBindingListRespVO> respList = bindings.stream()
                .map(this::convertToUserProjectBindingListRespVO)
                .collect(Collectors.groupingBy(
                        binding -> binding.getAdminUserId() + "_" + binding.getProjectId(),
                        Collectors.reducing((a, b) -> a.getDelsign() >= b.getDelsign() ? a : b)))
                .values()
                .stream()
                .filter(Optional::isPresent)
                .map(Optional::get)
                .collect(Collectors.toList());

        return respList;
    }

    /**
     * 更新管理员项目绑定
     */
    @Transactional
    public void updateUserProjectBinding(AdminAccessUserProjectBindingUpdateReqVO reqVO) {
        // 判断如果超级管理员,抛出 超级管理员不需要管理权限
        if (adminSuperAccessService.isSuperAdmin(reqVO.getUserId())) {
            throw new AdminException(4000, "超级管理员不需要管理权限");
        }

        // 创建用户项目绑定对象
        AdminUserProjectBinding binding = new AdminUserProjectBinding();
        binding.setAdminUserId(reqVO.getUserId());
        binding.setProjectId(reqVO.getProjectId());
        binding.setDelsign(reqVO.getDelsign() == 1);

        // 插入或更新用户项目绑定（冲突时更新delsign）
        int result = adminAccessDao.insertOrUpdateUserProjectBinding(binding);
        if (result <= 0) {
            throw new RuntimeException("更新用户项目绑定失败");
        }

        // 刷新Redis缓存
        adminAccessCacheService.refreshUserProjectBinding(reqVO);
    }

    // ==================== 用户路由绑定管理 ====================

    /**
     * 查询管理员路由绑定列表
     */
    public List<AdminAccessUserRouterBindingListRespVO> getUserRouterBindingList(
            AdminAccessUserRouterBindingListReqVO reqVO) {
        // 查询所有数据
        List<AdminUserRouterBinding> bindings = adminAccessDao.selectUserRouteBindingList(
                reqVO.getUserId(), reqVO.getRouterId(), reqVO.getDelsign());
        // 如果是超管，需要合并项目绑定
        if (adminSuperAccessService.isSuperAdmin(reqVO.getUserId())) {
            List<AdminUserRouterBinding> superProjectBindings = adminSuperAccessService
                    .getSuperAdminRouterBindings(reqVO.getUserId());
            bindings.addAll(superProjectBindings);
        }

        // 转换VO并按照adminUserId、adminRouterId分组,取delsign值最大的记录
        List<AdminAccessUserRouterBindingListRespVO> respList = bindings.stream()
                .map(this::convertToUserRouterBindingListRespVO)
                .collect(Collectors.groupingBy(
                        binding -> binding.getAdminUserId() + "_" + binding.getAdminRouterId(),
                        Collectors.reducing((a, b) -> a.getDelsign() >= b.getDelsign() ? a : b)))
                .values()
                .stream()
                .filter(Optional::isPresent)
                .map(Optional::get)
                .collect(Collectors.toList());
        return respList;
    }

    /**
     * 更新管理员路由绑定
     */
    @Transactional
    public void updateUserRouterBinding(AdminAccessUserRouterBindingUpdateReqVO reqVO) {
        // 判断如果超级管理员,抛出 超级管理员不需要管理权限
        if (adminSuperAccessService.isSuperAdmin(reqVO.getUserId())) {
            throw new AdminException(4000, "超级管理员不需要管理权限");
        }

        // 创建绑定对象
        AdminUserRouterBinding binding = new AdminUserRouterBinding();
        binding.setAdminUserId(reqVO.getUserId());
        binding.setAdminRouterId(reqVO.getRouterId());
        binding.setDelsign(reqVO.getDelsign() == 1);

        // 插入或更新绑定
        int result = adminAccessDao.insertOrUpdateUserRouterBinding(binding);
        if (result <= 0) {
            throw new AdminException(400, "更新管理员路由绑定失败");
        }

        // 刷新Redis缓存
        adminAccessCacheService.refreshUserRouterBinding(reqVO);
    }

    // ==================== 角色管理 ====================

    /**
     * 查询角色列表
     */
    public List<AdminAccessRoleListRespVO> getRoleList(AdminAccessRoleListReqVO reqVO) {
        // 查询所有数据
        List<AdminRole> roles = adminAccessDao.selectRoleList(
                reqVO.getName(), reqVO.getDelsign());

        // 转换VO
        List<AdminAccessRoleListRespVO> respList = roles.stream()
                .map(this::convertToRoleListRespVO)
                .collect(Collectors.toList());

        return respList;
    }

    /**
     * 创建角色
     */
    @Transactional
    public void createRole(AdminAccessRoleCreateReqVO reqVO) {
        AdminRole adminRole = new AdminRole();
        adminRole.setName(reqVO.getAdminRoleName());
        adminRole.setDescription(reqVO.getDescription());

        int result = adminAccessDao.insertRole(adminRole);
        if (result <= 0) {
            throw new RuntimeException("创建角色失败");
        }
    }

    /**
     * 删除角色
     */
    @Transactional
    public void deleteRole(AdminAccessRoleDeleteReqVO reqVO) {
        int result = adminAccessDao.deleteRole(reqVO.getAdminRoleId());
        if (result <= 0) {
            throw new RuntimeException("删除角色失败");
        }
    }

    // ==================== 角色路由绑定管理 ====================

    /**
     * 查询角色路由绑定列表
     */
    public List<AdminAccessRoleRouterBindingListRespVO> getRoleRouterBindingList(
            AdminAccessRoleRouterBindingListReqVO reqVO) {
        // 查询所有数据
        List<AdminRoleRouterBindingDTO> bindings = adminAccessDao.selectRoleRouteBindingList(
                reqVO.getAdminRoleId(), reqVO.getAdminRouterId(), reqVO.getDelsign());
        //
        if (adminSuperAccessService.isSuperAdminByRole(reqVO.getAdminRoleId())) {
            List<AdminRoleRouterBindingDTO> superBindings = adminSuperAccessService.getSuperRoleRouterBindings();
            bindings.addAll(superBindings);
        }
        // 转换VO
        // 转换VO并按照adminUserId、adminRouterId分组,取delsign值最大的记录
        List<AdminAccessRoleRouterBindingListRespVO> respList = bindings.stream()
                .map(this::convertToRoleRouterBindingListRespVO)
                .collect(Collectors.groupingBy(
                        binding -> binding.getAdminRoleId() + "_" + binding.getAdminRouterId(),
                        Collectors.reducing((a, b) -> a.getDelsign() >= b.getDelsign() ? a : b)))
                .values()
                .stream()
                .filter(Optional::isPresent)
                .map(Optional::get)
                .collect(Collectors.toList());

        return respList;
    }

    /**
     * 更新角色路由绑定
     */
    @Transactional
    public void updateRoleRouterBinding(AdminAccessRoleRouterBindingUpdateReqVO reqVO) {
        if (adminSuperAccessService.isSuperAdminByRole(reqVO.getAdminRoleId())) {
            throw new AdminException(4000, "超级管理员不需要管理权限");
        }
        // 创建绑定对象
        AdminRoleRouterBinding binding = new AdminRoleRouterBinding();
        binding.setAdminRoleId(reqVO.getAdminRoleId());
        binding.setAdminRouterId(reqVO.getAdminRouterId());
        binding.setDelsign(reqVO.getDelsign() == 1);

        // 插入或更新绑定
        int result = adminAccessDao.insertOrUpdateRoleRouterBinding(binding);
        if (result <= 0) {
            throw new AdminException(4000, "更新角色路由绑定失败");
        }

        // 刷新Redis缓存
        adminAccessCacheService.refreshRoleRouterBinding(reqVO);
    }

    // ==================== 角色项目绑定管理 ====================

    /**
     * 查询角色项目绑定列表
     */
    public List<AdminAccessRoleProjectBindingListRespVO> getRoleProjectBindingList(
            AdminAccessRoleProjectBindingListReqVO reqVO) {
        // 查询所有数据
        List<AdminRoleProjectBindingDTO> bindings = adminAccessDao.selectRoleProjectBindingList(
                reqVO.getAdminRoleId(), reqVO.getProjectId(), reqVO.getDelsign());

        if (adminSuperAccessService.isSuperAdminByRole(reqVO.getAdminRoleId())) {
            List<AdminRoleProjectBindingDTO> superBindings = adminSuperAccessService.getSuperRoleProjectBindings();
            bindings.addAll(superBindings);
        }
        // 转换VO
        List<AdminAccessRoleProjectBindingListRespVO> respList = bindings.stream()
                .map(this::convertToRoleProjectBindingListRespVO)
                .collect(Collectors.groupingBy(
                        binding -> binding.getAdminRoleId() + "_" + binding.getProjectId(),
                        Collectors.reducing((a, b) -> a.getDelsign() >= b.getDelsign() ? a : b)))
                .values()
                .stream()
                .filter(Optional::isPresent)
                .map(Optional::get)
                .collect(Collectors.toList());

        return respList;
    }

    /**
     * 更新角色项目绑定
     */
    @Transactional
    public void updateRoleProjectBinding(AdminAccessRoleProjectBindingUpdateReqVO reqVO) {
        if (adminSuperAccessService.isSuperAdminByRole(reqVO.getAdminRoleId())) {
            throw new AdminException(4000, "超级管理员不需要管理权限");
        }

        // 循环插入新绑定
        AdminRoleProjectBinding binding = new AdminRoleProjectBinding();
        binding.setAdminRoleId(reqVO.getAdminRoleId());
        binding.setProjectId(reqVO.getProjectId());
        binding.setDelsign(reqVO.getDelsign() == 1); // 设置为未删除状态

        int result = adminAccessDao.insertOrUpdateRoleProjectBinding(binding);
        if (result <= 0) {
            throw new AdminException(4000, "更新角色项目绑定失败");
        }

        // 刷新Redis缓存
        adminAccessCacheService.refreshRoleProjectBinding(reqVO);

    }

    // ==================== 角色用户绑定管理 ====================

    /**
     * 查询角色用户绑定列表
     */
    public List<AdminAccessRoleUserBindingListRespVO> getRoleUserBindingList(
            AdminAccessRoleUserBindingListReqVO reqVO) {
        // 查询所有数据
        List<AdminUserRoleBindingDTO> bindings = adminAccessDao.selectRoleUserBindingList(
                reqVO.getAdminRoleId(), reqVO.getAdminUserId(), reqVO.getDelsign());
        // 转换VO 
        List<AdminAccessRoleUserBindingListRespVO> respList = bindings.stream()
                .map(this::convertToRoleUserBindingListRespVO)
                .collect(Collectors.toList());

        return respList;
    }

    /**
     * 更新角色用户绑定
     */
    @Transactional
    public void updateRoleUserBinding(AdminAccessRoleUserBindingUpdateReqVO reqVO) {

        AdminUserRoleBinding binding = new AdminUserRoleBinding();
        binding.setAdminRoleId(reqVO.getAdminRoleId());
        binding.setAdminUserId(reqVO.getAdminUserId());
        binding.setDelsign(reqVO.getDelsign() == 1); // 设置为未删除状态

        int result = adminAccessDao.insertOrUpdateRoleUserBinding(binding);
        if (result <= 0) {
            throw new AdminException(4000, "更新角色用户绑定失败");
        }

        // 刷新Redis缓存
        adminAccessCacheService.refreshRoleUserBinding(reqVO);
    }

    // ==================== 路由管理 ====================

    /**
     * 查询路由列表
     */
    public List<AdminAccessRouterListRespVO> getRouterList(AdminAccessRouterListReqVO reqVO) {
        // 查询所有数据
        List<AdminRouter> routers = adminAccessDao.selectRouterList(
                reqVO.getBackendPath(), reqVO.getFrontendPath(), reqVO.getFrontendName(),
                reqVO.getDescription(), reqVO.getParentAdminRouterId(), reqVO.getDelsign());

        // 转换VO
        List<AdminAccessRouterListRespVO> respList = routers.stream()
                .map(this::convertToRouterListRespVO)
                .collect(Collectors.toList());

        return respList;
    }

    // ==================== 私有转换方法 ====================

    private AdminAccessUserListRespVO convertToUserListRespVO(AdminUser user) {
        AdminAccessUserListRespVO respVO = new AdminAccessUserListRespVO();
        respVO.setId(user.getId());
        respVO.setUsername(user.getUsername());
        respVO.setLastLoginTime(user.getLastLoginTime());
        respVO.setCreateTime(user.getCreateTime());
        respVO.setDelsign(user.getDelsign() ? 1 : 0);
        return respVO;
    }

    private AdminAccessUserProjectBindingListRespVO convertToUserProjectBindingListRespVO(
            AdminUserProjectBindingDTO binding) {
        AdminAccessUserProjectBindingListRespVO respVO = new AdminAccessUserProjectBindingListRespVO();
        respVO.setAdminUserId(binding.getAdminUserId());
        respVO.setProjectId(binding.getProjectId());
        respVO.setProjectName(binding.getProjectName());
        respVO.setDelsign(binding.getDelsign() ? 1 : 0);
        return respVO;
    }

    private AdminAccessUserRouterBindingListRespVO convertToUserRouterBindingListRespVO(
            AdminUserRouterBinding binding) {
        AdminAccessUserRouterBindingListRespVO respVO = new AdminAccessUserRouterBindingListRespVO();
        respVO.setAdminUserId(binding.getAdminUserId());
        respVO.setAdminRouterId(binding.getAdminRouterId());
        respVO.setDelsign(binding.getDelsign() ? 1 : 0);
        // TODO: 需要关联查询用户名和路由信息
        return respVO;
    }

    private AdminAccessRoleListRespVO convertToRoleListRespVO(AdminRole role) {
        AdminAccessRoleListRespVO respVO = new AdminAccessRoleListRespVO();
        respVO.setAdminRoleId(role.getId());
        respVO.setAdminRoleName(role.getName());
        respVO.setAdminRoleDescription(role.getDescription());
        respVO.setCreateTime(role.getCreateTime());
        respVO.setUpdateTime(role.getUpdateTime());
        respVO.setDelsign(role.getDelsign() ? 1 : 0);
        return respVO;
    }

    private AdminAccessRoleRouterBindingListRespVO convertToRoleRouterBindingListRespVO(
            AdminRoleRouterBindingDTO binding) {
        AdminAccessRoleRouterBindingListRespVO respVO = new AdminAccessRoleRouterBindingListRespVO();
        respVO.setAdminRoleId(binding.getAdminRoleId());
        respVO.setAdminRouterId(binding.getAdminRouterId());
        respVO.setDelsign(binding.getDelsign() ? 1 : 0);
        respVO.setRouterName(binding.getRouterName());
        return respVO;
    }

    private AdminAccessRoleProjectBindingListRespVO convertToRoleProjectBindingListRespVO(
            AdminRoleProjectBindingDTO binding) {
        AdminAccessRoleProjectBindingListRespVO respVO = new AdminAccessRoleProjectBindingListRespVO();
        respVO.setAdminRoleId(binding.getAdminRoleId());
        respVO.setProjectId(binding.getProjectId());
        respVO.setDelsign(binding.getDelsign() ? 1 : 0);
        respVO.setProjectName(binding.getProjectName());
        return respVO;
    }

    private AdminAccessRoleUserBindingListRespVO convertToRoleUserBindingListRespVO(AdminUserRoleBindingDTO binding) {
        AdminAccessRoleUserBindingListRespVO respVO = new AdminAccessRoleUserBindingListRespVO();
        respVO.setAdminRoleId(binding.getAdminRoleId());
        respVO.setAdminUserId(binding.getAdminUserId());
        respVO.setDelsign(binding.getDelsign() ? 1 : 0);
        // 设置角色名称
        respVO.setAdminUserName(binding.getAdminUserName());
        return respVO;
    }

    private AdminAccessRouterListRespVO convertToRouterListRespVO(AdminRouter router) {
        AdminAccessRouterListRespVO respVO = new AdminAccessRouterListRespVO();
        respVO.setId(router.getId());
        respVO.setBackendPath(router.getBackendPath());
        respVO.setFrontendPath(router.getFrontendPath());
        respVO.setFrontendName(router.getFrontendName());
        respVO.setDescription(router.getDescription());
        respVO.setParentAdminRouterId(router.getParentAdminRouterId());
        respVO.setCreateTime(router.getCreateTime());
        respVO.setUpdateTime(router.getUpdateTime());
        respVO.setDelsign(router.getDelsign() ? 1 : 0);
        return respVO;
    }

}
package com.mega.platform.cloud.admin.vo;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * 项目详情响应参数
 */
@Data
@Accessors(chain = true)
@ApiModel("项目详情响应参数")
public class AdminProjectVO {
    @ApiModelProperty("项目ID")
    private Long id;
    
    @ApiModelProperty("项目名称")
    private String name;
    
    @ApiModelProperty("状态 0：不可用 1：正常 2：挂起 3：审核中")
    private Integer status;
    
    @ApiModelProperty("状态描述")
    private String statusDesc;
    
    @ApiModelProperty("描述")
    private String remark;
    
    @ApiModelProperty("创建时间")
    private Date createTime;
}
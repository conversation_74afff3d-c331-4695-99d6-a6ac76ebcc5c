package com.mega.platform.cloud.admin.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * 管理员登录响应数据
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@ApiModel("管理员登录响应数据")
public class AdminAuthLoginRespVO {

    @ApiModelProperty(value = "访问令牌", example = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...")
    private String token;

}
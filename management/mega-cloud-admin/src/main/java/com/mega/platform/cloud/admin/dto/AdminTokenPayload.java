package com.mega.platform.cloud.admin.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;
import java.util.Set;

import io.swagger.annotations.ApiModelProperty;

@Data
@Accessors(chain = true)
public class AdminTokenPayload {
 
     @ApiModelProperty("所有项目ID列表")
     private Set<Long> projectIds;

     @ApiModelProperty("所有后端列表")
     private Set<String> backendPaths;

     @ApiModelProperty("所有角色id列表")
     private Set<Long> roleIds;

     @ApiModelProperty("token版本号")
     private Long tokenVersion;

     @ApiModelProperty("用户id")
     private Long adminUserId;
}

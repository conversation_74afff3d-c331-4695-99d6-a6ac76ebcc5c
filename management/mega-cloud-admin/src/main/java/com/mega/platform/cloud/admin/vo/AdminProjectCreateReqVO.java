package com.mega.platform.cloud.admin.vo;

import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * 项目创建请求参数
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "项目创建请求参数", description = "项目创建请求参数")
public class AdminProjectCreateReqVO {
    
    /**
     * 项目名称
     */
    @NotBlank(message = "项目名称不能为空")
    @ApiModelProperty(value = "项目名称", required = true, example = "项目名称")
    private String name;
    
    /**
     * 描述
     */
    @ApiModelProperty(value = "描述", required = false, example = "描述")
    private String remark;
}
package com.mega.platform.cloud.admin.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 权限管理-角色项目绑定更新请求参数
 */
@Data
@Accessors(chain = true)
@ApiModel("权限管理-角色项目绑定更新请求参数")
public class AdminAccessRoleProjectBindingUpdateReqVO {

    @ApiModelProperty(value = "角色ID", example = "1")
    @NotNull(message = "角色ID不能为空")
    private Long adminRoleId;

    @ApiModelProperty(value = "项目ID", example = "1")
    @NotNull(message = "项目ID不能为空")
    private Long projectId;

    @ApiModelProperty(value = "是否删除", example = "0")
    @NotNull(message = "是否删除不能为空")
    private Integer delsign;

}
package com.mega.platform.cloud.admin.constant;

/**
 * 管理员认证相关常量
 *
 * <AUTHOR>
 */
public class AdminAuthConstant {

    /**
     * Context中存储管理员用户ID的key
     */
    public static final String CONTEXT_ADMIN_USER_ID = "adminUserId";

    /**
     * Context中存储管理员用户角色列表的key
     */
    public static final String CONTEXT_ADMIN_USER_ROLES = "adminUserRoles";

    /**
     * Context中存储项目ID列表的key
     */
    public static final String CONTEXT_PROJECT_IDS = "projectIds";

    /**
     * Context中存储路由权限列表的key
     */
    public static final String CONTEXT_PERMISSIONS = "permissions";

    /**
     * Context中存储令牌版本的key
     */
    public static final String CONTEXT_TOKEN_VERSION = "tokenVersion";

    /**
     * JWT密钥 (256位/32字节，符合RFC 7518安全要求)
     */
    public static final String JWT_SECRET = "mega-cloud-admin-jwt-secret-key-256bit-secure-hmac-sha-algorithm";

    /**
     * JWT过期时间（7天，单位：秒）
     */
    public static final long JWT_EXPIRATION_TIME = 7 * 24 * 60 * 60L;

    /**
     * Redis中存储用户完整信息payload的key前缀
     */
    public static final String REDIS_USER_PAYLOAD_PREFIX = "mega-cloud-admin:user:payload:";

    /**
     * Redis缓存过期时间（7天，单位：秒）
     */
    public static final long REDIS_EXPIRATION_TIME = 7 * 24 * 60 * 60L;

}
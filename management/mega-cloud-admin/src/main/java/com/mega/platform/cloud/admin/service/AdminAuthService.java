package com.mega.platform.cloud.admin.service;

import com.mega.platform.cloud.AdminException;
import com.mega.platform.cloud.admin.constant.AdminAuthConstant;
import com.mega.platform.cloud.admin.dao.AdminAuthDao;
import com.mega.platform.cloud.admin.vo.AdminAuthLoginReqVO;
import com.mega.platform.cloud.admin.vo.AdminAuthLoginRespVO;
import com.mega.platform.cloud.admin.vo.AdminAuthProfileRespVO;
import com.mega.platform.cloud.data.entity.AdminRole;
import com.mega.platform.cloud.data.entity.AdminRouter;
import com.mega.platform.cloud.data.entity.AdminUser;
import com.mega.platform.cloud.data.entity.Project;
import io.jsonwebtoken.Claims;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.SignatureAlgorithm;
import io.jsonwebtoken.security.Keys;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.security.Key;
import java.util.*;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.mega.platform.cloud.admin.vo.AdminAuthProfileRespVO.AdminRoleVO;
import com.mega.platform.cloud.admin.vo.AdminAuthProfileRespVO.AdminSecondRouterVO;
import com.mega.platform.cloud.admin.vo.AdminAuthProfileRespVO.AdminThirdRouterVO;
import com.mega.platform.cloud.admin.vo.AdminAuthProfileRespVO.AdminProjectVO;
import com.mega.platform.cloud.admin.vo.AdminAuthProfileRespVO.AdminFirstRouterVO;
import com.mega.platform.cloud.admin.dto.AdminTokenPayload;
import com.mega.platform.cloud.admin.util.AdminCryptUtils;

import java.util.stream.Collectors;
import java.util.concurrent.TimeUnit;

/**
 * 管理员认证服务层
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class AdminAuthService {

    private final AdminAuthDao adminAuthDao;
    private final AdminAccessCacheService adminAccessCacheService;
    

    @Autowired
    public AdminAuthService(AdminAuthDao adminAuthDao, AdminAccessCacheService adminAccessCacheService) {
        this.adminAuthDao = adminAuthDao;
        this.adminAccessCacheService = adminAccessCacheService;
    }

    /**
     * 角色权限+直接分配权限
     * 
     * @param adminUserId
     * @param roleIds
     * @return
     */
    private Set<AdminRouter> getALlRouters(Long adminUserId, Set<Long> roleIds) {
        List<AdminRouter> roleRouters = CollectionUtils.isEmpty(roleIds) ? new ArrayList<>()
                : adminAuthDao.findRoutersByRoleIds(roleIds.stream().collect(Collectors.toList()));
        List<AdminRouter> userRouters = adminAuthDao.findRoutersByAdminUserId(adminUserId);
        // 合并权限并去重
        Set<AdminRouter> allRouters = new HashSet<>(roleRouters);
        allRouters.addAll(userRouters);
        return allRouters;
    }

    private Set<Project> getAllProkects(Long adminUserId, Set<Long> roleIds) {
        List<Project> roleProjects = CollectionUtils.isEmpty(roleIds) ? new ArrayList<>()
                : adminAuthDao.findProjectsByRoleIds(roleIds.stream().collect(Collectors.toList()));
        List<Project> userProjects = adminAuthDao.findProjectsByAdminUserId(adminUserId);
        // 合并项目并去重
        Set<Project> allProjects = new HashSet<>(roleProjects);
        allProjects.addAll(userProjects);
        return allProjects;
    }

    /**
     * 管理员登录
     *
     * @param reqVO 登录请求参数
     * @return 登录响应结果
     */
    @Transactional
    public AdminAuthLoginRespVO login(AdminAuthLoginReqVO reqVO) {

        // 1. 验证用户名和密码
        AdminUser adminUser = adminAuthDao.findByUsername(reqVO.getUsername());
        if (adminUser == null) {
            throw new AdminException(4000, "用户名或密码错误");
        }
        if (!adminUser.getPassword().equals(reqVO.getPassword())) {
            throw new AdminException(4000, "用户名或密码错误");
        }

        // 2. 查询用户角色
        List<AdminRole> roles = adminAuthDao.findRolesByAdminUserId(adminUser.getId());
        Set<Long> roleIds = roles.stream().map(AdminRole::getId).collect(Collectors.toSet());

        // 3. 查询用户权限（角色权限 + 直接分配权限）
        Set<AdminRouter> allRouters = getALlRouters(adminUser.getId(), roleIds);
        Set<String> permissions = allRouters.stream()
                .map(AdminRouter::getBackendPath)
                .filter(StringUtils::hasText)
                .collect(Collectors.toSet());

        // 4. 查询用户项目（角色项目 + 直接分配项目）
        Set<Project> allProjects = getAllProkects(adminUser.getId(), roleIds);
        Set<Long> projectIds = allProjects.stream().map(Project::getId).collect(Collectors.toSet());

        // 5. 生成新的token版本
        String token = adminAccessCacheService.refreshToken(adminUser.getId(), roleIds, projectIds, permissions);
        // 11. 更新最后登录时间
        adminAuthDao.updateLastLoginTime(adminUser.getId());

        // 12. 构建响应结果
        AdminAuthLoginRespVO respVO = new AdminAuthLoginRespVO();
        respVO.setToken(token);


        return respVO;
    }

    /**
     * 获取管理员用户详细信息
     *
     * @param adminUserId 管理员用户ID
     * @return 管理员用户详细信息
     */
    public AdminAuthProfileRespVO getProfile(Long adminUserId) {
        // 1. 查询用户信息
        AdminUser adminUser = adminAuthDao.findById(adminUserId);
        if (adminUser == null) {
            throw new AdminException(4000, "用户不存在");
        }

        // 2. 查询用户角色
        List<AdminRole> roles = adminAuthDao.findRolesByAdminUserId(adminUser.getId());
        Set<Long> roleIds = roles.stream().map(AdminRole::getId).collect(Collectors.toSet());

        // 构建角色VO列表
        List<AdminRoleVO> roleVOs = roles.stream().map(role -> {
            AdminRoleVO roleVO = new AdminRoleVO();
            roleVO.setId(role.getId());
            roleVO.setName(role.getName());
            roleVO.setDescription(role.getDescription());
            return roleVO;
        }).collect(Collectors.toList());

        // 3. 查询用户权限（角色权限 + 直接分配权限）
        Set<AdminRouter> allRouters = getALlRouters(adminUser.getId(), roleIds);
        // 4. 查询用户项目（角色项目 + 直接分配项目）
        Set<Project> allProjects = getAllProkects(adminUser.getId(), roleIds);
        // 
        List<AdminFirstRouterVO> firstRouterVOs = boxFrontendRouters(allRouters, allProjects);

        // 构建项目VO列表
        List<AdminProjectVO> projectVOs = allProjects.stream().map(project -> {
            AdminProjectVO projectVO = new AdminProjectVO();
            projectVO.setId(project.getId());
            projectVO.setName(project.getName());
            return projectVO;
        }).collect(Collectors.toList());

        // 5. 构建响应结果
        AdminAuthProfileRespVO respVO = new AdminAuthProfileRespVO();
        respVO.setAdminUserId(adminUser.getId());
        respVO.setAdminUserName(adminUser.getUsername());
        respVO.setRoles(roleVOs);
        respVO.setProjects(projectVOs);
        respVO.setRouters(firstRouterVOs);
        // log.info("获取管理员用户详细信息成功，用户ID: {}, 用户名: {}", adminUser.getId(),
        // adminUser.getUsername());
        return respVO;
    }

    private List<AdminFirstRouterVO> boxFrontendRouters(Set<AdminRouter> allRouters, Set<Project> allProjects) {
        // 处理前端路由 - 构建父子路由关系
        List<AdminFirstRouterVO> firstRouters = new ArrayList<>();
        //系统级别路由
        Map<Long, List<AdminSecondRouterVO>> systemContentMap = new HashMap<>();
        //项目级别路由
        List<AdminSecondRouterVO> projectRouters = new ArrayList<>();
        Map<Long, List<AdminThirdRouterVO>>  projectContentMap = new HashMap<>();


        // 使用一个循环处理所有路由
        for (AdminRouter router : allRouters) {
            // 跳过没有前端路径的路由
            if (!StringUtils.hasText(router.getFrontendPath())) {
                continue;
            }
            log.info("routerId: {}", router.getId());

            // system级别
            if (router.getRouterCate() == 1) {
                if (router.getParentAdminRouterId() == null || router.getParentAdminRouterId() == 0L) {
                    AdminFirstRouterVO parentRouterVO = new AdminFirstRouterVO();
                    parentRouterVO.setId(router.getId());
                    parentRouterVO.setFrontendPath(router.getFrontendPath());
                    parentRouterVO.setFrontendName(router.getFrontendName());
                    parentRouterVO.setChildRouters(new ArrayList<>());
                    firstRouters.add(parentRouterVO);
                } else {
                    AdminSecondRouterVO childRouterVO = new AdminSecondRouterVO();
                    childRouterVO.setId(router.getId());
                    childRouterVO.setFrontendPath(router.getFrontendPath());
                    childRouterVO.setFrontendName(router.getFrontendName());
                    systemContentMap.computeIfAbsent(router.getParentAdminRouterId(), k -> new ArrayList<>())
                        .add(childRouterVO);
                }
            }
            // project级别
            else if(router.getRouterCate() == 2){
                if (router.getParentAdminRouterId() == null || router.getParentAdminRouterId() == 0L) {
                    AdminSecondRouterVO secondRouterVO = new AdminSecondRouterVO();
                    secondRouterVO.setId(router.getId());
                    secondRouterVO.setFrontendPath(router.getFrontendPath());
                    secondRouterVO.setFrontendName(router.getFrontendName());
                    secondRouterVO.setChildRouters(new ArrayList<>());
                    projectRouters.add(secondRouterVO);
                } else {
                    AdminThirdRouterVO childRouterVO = new AdminThirdRouterVO();
                    childRouterVO.setId(router.getId());
                    childRouterVO.setFrontendPath(router.getFrontendPath());
                    childRouterVO.setFrontendName(router.getFrontendName());
                    projectContentMap.computeIfAbsent(router.getParentAdminRouterId(), k -> new ArrayList<>())
                        .add(childRouterVO);
                }
            }
        }

        // 系统级别存入
        for (AdminFirstRouterVO firstRouterVO : firstRouters) {
            firstRouterVO.setChildRouters(systemContentMap.get(firstRouterVO.getId()));
        }

        // 项目路由录入
        for (AdminSecondRouterVO secondRouterVO : projectRouters) {
            secondRouterVO.setChildRouters(projectContentMap.get(secondRouterVO.getId()));
        }
        // 对项目列表按ID排序
        List<Project> sortedProjects = allProjects.stream()
                .sorted(Comparator.comparing(Project::getId))
                .collect(Collectors.toList());

        for (Project project : sortedProjects) {
            AdminFirstRouterVO firstRouterVO = new AdminFirstRouterVO();
            firstRouterVO.setId(project.getId() * 10000);
            firstRouterVO.setFrontendPath("/" + project.getId());
            firstRouterVO.setFrontendName(project.getName());

            // 对二级路由按ID排序
            List<AdminSecondRouterVO> sortedSecondRouters = projectRouters.stream()
                    .sorted(Comparator.comparing(AdminSecondRouterVO::getId))
                    .peek(router -> {
                        if (router.getChildRouters() != null) {
                            // 对三级路由按ID排序
                            List<AdminThirdRouterVO> sortedThirdRouters = router.getChildRouters().stream()
                                    .sorted(Comparator.comparing(AdminThirdRouterVO::getId))
                                    .peek(thirdRouter -> {
                                        thirdRouter.setFrontendPath(firstRouterVO.getFrontendPath() + thirdRouter.getFrontendPath());
                                    })
                                    .collect(Collectors.toList());
                            router.setChildRouters(sortedThirdRouters);
                        }
                        router.setFrontendPath(firstRouterVO.getFrontendPath() + router.getFrontendPath());
                    })
                    .collect(Collectors.toList());

            firstRouterVO.setChildRouters(sortedSecondRouters);
            firstRouters.add(firstRouterVO);
        }
        return firstRouters;
    }

    /**
     * 管理员登出
     *
     * @param adminUserId 管理员用户ID
     */
    public void logout(Long adminUserId) {
        log.info("管理员登出成功，用户ID: {}", adminUserId);
    }

    /**
     * 管理员全部登出
     *
     * @param adminUserId 管理员用户ID
     */
    public void logoutAll(Long adminUserId) {
        adminAccessCacheService.deleteToken(adminUserId);
    }
}
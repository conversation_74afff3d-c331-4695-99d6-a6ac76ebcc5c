package com.mega.platform.cloud.admin.vo;

import com.mega.platform.cloud.data.entity.AuthAliyunSmsConfig;
import com.mega.platform.cloud.data.entity.AuthAppleConfig;
import com.mega.platform.cloud.data.entity.AuthGoogleConfig;
import com.mega.platform.cloud.data.entity.AuthWeChatConfig;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@ApiModel("应用auth配置")
public class AdminAppAuthConfigReqVO {
    @ApiModelProperty("应用ID")
    private Long projectAppId;

    @ApiModelProperty("第三方通用平台配置")
    private Long thirdPlatformId;

    @ApiModelProperty("阿里云短信配置")
    private AuthAliyunSmsConfig authAliyunSmsConfig;

    @ApiModelProperty("微信登录相关配置")
    private AuthWeChatConfig authWeChatConfig;

    @ApiModelProperty("谷歌登录相关配置")
    private AuthGoogleConfig authGoogleConfig;

    @ApiModelProperty("苹果登录相关配置")
    private AuthAppleConfig authAppleConfig;

}

package com.mega.platform.cloud.admin.dao;

import com.mega.platform.cloud.data.entity.AdminRouter;
import com.mega.platform.cloud.data.entity.AdminUserProjectBinding;
import com.mega.platform.cloud.data.entity.AdminUserRouterBinding;
import com.mega.platform.cloud.data.entity.Project;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 超级管理员权限管理数据访问层
 */
@Mapper
public interface AdminSuperAccessDao {

    /**
     * 查询传入的userId是否是超级管理员(role_id = 1)
     * @param userId 用户ID
     * @return 是否为超级管理员
     */
    boolean isSuperAdmin(@Param("userId") Long userId);

    /**
     * 查询所有项目列表
     * @return 项目列表
     */
    List<Project> selectAllProjects();

    /**
     * 查询所有路由列表
     * @return 路由列表
     */
    List<AdminRouter> selectAllRouters();

}
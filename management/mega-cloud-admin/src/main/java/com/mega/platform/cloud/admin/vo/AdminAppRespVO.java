package com.mega.platform.cloud.admin.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

@Data
@Accessors(chain = true)
@ApiModel("应用响应数据")
public class AdminAppRespVO {
    
    @ApiModelProperty("应用ID")
    private Long id;
    
    @ApiModelProperty("应用Key")
    private String appKey;
    
    @ApiModelProperty("应用密钥")
    private String appSecret;
    
    @ApiModelProperty("应用名称")
    private String name;
    
    @ApiModelProperty("项目ID")
    private Long projectId;
    
    @ApiModelProperty("状态：0不可用，1正常，2挂起，3审核中")
    private Integer status;
    
    @ApiModelProperty("描述")
    private String remark;
    
    @ApiModelProperty("创建时间")
    private Date createTime;
    
    @ApiModelProperty("更新时间")
    private Date updateTime;
}
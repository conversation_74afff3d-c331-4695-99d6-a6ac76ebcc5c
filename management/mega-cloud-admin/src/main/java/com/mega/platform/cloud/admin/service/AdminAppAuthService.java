package com.mega.platform.cloud.admin.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.mega.platform.cloud.AdminErrorCode;
import com.mega.platform.cloud.AdminException;
import com.mega.platform.cloud.admin.vo.AdminAppAuthConfigReqVO;
import com.mega.platform.cloud.admin.vo.AdminAppAuthHandleConfigReqVO;
import com.mega.platform.cloud.admin.vo.AdminAppAuthSmsReqVO;
import com.mega.platform.cloud.common.enums.ThirdPlatformEnum;
import com.mega.platform.cloud.common.mapper.AuthAppConfigMapper;
import com.mega.platform.cloud.common.mapper.AuthSmsTemplateConfigMapper;
import com.mega.platform.cloud.data.entity.AuthAppConfig;
import com.mega.platform.cloud.data.entity.AuthSmsTemplateConfig;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@Slf4j
@RequiredArgsConstructor
public class AdminAppAuthService {
    private final AuthAppConfigMapper authAppConfigMapper;

    private final AuthSmsTemplateConfigMapper authSmsTemplateConfigMapper;
    public void authConfigCreate(Long projectId, AdminAppAuthConfigReqVO reqVO) throws JsonProcessingException {
        Long thirdPlatformId = reqVO.getThirdPlatformId();
        String config;
        ObjectMapper objectMapper = new ObjectMapper();
        switch (ThirdPlatformEnum.fromCode(thirdPlatformId)) {
            case WECHAT:
                config = objectMapper.writeValueAsString(reqVO.getAuthWeChatConfig());
                break;
            case APPLE:
                config = objectMapper.writeValueAsString(reqVO.getAuthAppleConfig());
                break;
            case ALIPAY:
                config = objectMapper.writeValueAsString(reqVO.getAuthAliyunSmsConfig());
                break;
            case GOOGLE:
                config = objectMapper.writeValueAsString(reqVO.getAuthGoogleConfig());
                break;
            default:
                throw new AdminException(AdminErrorCode.ERR_0);
        }
        AuthAppConfig authAppConfig = new AuthAppConfig().setProjectAppId(projectId).setConfig(config).setThirdPlatformId(thirdPlatformId);
        authAppConfigMapper.insertSelective(authAppConfig);
    }

    public void authConfigDelete(Long appId, Long thirdPlatformId) {
        authAppConfigMapper.delete(new AuthAppConfig().setProjectAppId(appId).setThirdPlatformId(thirdPlatformId));
    }

    public void authConfigUpdate(Long projectId, AdminAppAuthConfigReqVO reqVO) throws JsonProcessingException {
        Long thirdPlatformId = reqVO.getThirdPlatformId();
        String config;
        ObjectMapper objectMapper = new ObjectMapper();
        switch (ThirdPlatformEnum.fromCode(thirdPlatformId)) {
            case WECHAT:
                config = objectMapper.writeValueAsString(reqVO.getAuthWeChatConfig());
                break;
            case APPLE:
                config = objectMapper.writeValueAsString(reqVO.getAuthAppleConfig());
                break;
            case ALIPAY:
                config = objectMapper.writeValueAsString(reqVO.getAuthAliyunSmsConfig());
                break;
            case GOOGLE:
                config = objectMapper.writeValueAsString(reqVO.getAuthGoogleConfig());
                break;
            default:
                throw new AdminException(AdminErrorCode.ERR_0);
        }

        AuthAppConfig update = new AuthAppConfig()
                .setProjectAppId(projectId)
                .setThirdPlatformId(thirdPlatformId)
                .setConfig(config);

        authAppConfigMapper.updateByPrimaryKeySelective(update);
    }

    public List<AuthAppConfig> authConfigList(AdminAppAuthHandleConfigReqVO reqVO) {
        return authAppConfigMapper.select(new AuthAppConfig().setProjectAppId(reqVO.getProjectAppId()).setThirdPlatformId(reqVO.getThirdPlatformId()));
    }

    public void save(AdminAppAuthSmsReqVO reqVO) {
        AuthSmsTemplateConfig config = new AuthSmsTemplateConfig();
        BeanUtils.copyProperties(reqVO, config);
        authSmsTemplateConfigMapper.insertSelective(config);
    }

    public void updateById(AdminAppAuthSmsReqVO reqVO) {
        AuthSmsTemplateConfig config = new AuthSmsTemplateConfig();
        BeanUtils.copyProperties(reqVO, config);
        authSmsTemplateConfigMapper.updateByPrimaryKeySelective(config);
    }

    public void removeById(Long id) {
        authSmsTemplateConfigMapper.deleteByPrimaryKey(id);
    }


    public List<AuthSmsTemplateConfig> smsConfigList(AdminAppAuthSmsReqVO reqVO ) {
        AuthSmsTemplateConfig config = new AuthSmsTemplateConfig();
        BeanUtils.copyProperties(reqVO, config);
        List<AuthSmsTemplateConfig> authSmsTemplateConfigs;
        authSmsTemplateConfigs = authSmsTemplateConfigMapper.select(config);
        return authSmsTemplateConfigs;
    }
}

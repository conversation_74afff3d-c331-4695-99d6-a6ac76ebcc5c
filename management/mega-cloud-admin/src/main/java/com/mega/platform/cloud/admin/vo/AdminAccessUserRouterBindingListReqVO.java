package com.mega.platform.cloud.admin.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;

/**
 * 权限管理-管理员路由绑定列表请求
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@ApiModel("权限管理-管理员路由绑定列表请求")
public class AdminAccessUserRouterBindingListReqVO {

    @ApiModelProperty("管理员ID")
    @NotNull(message = "管理员ID不能为空")
    private Long userId;

    // @ApiModelProperty("页码")
    // private Integer pageNum = 1;

    // @ApiModelProperty("每页大小")
    // private Integer pageSize = 10;

    @ApiModelProperty("路由ID")
    private Long routerId;

    @ApiModelProperty("删除标识: 0=未删除, 1=已删除")
    private Integer delsign;
}
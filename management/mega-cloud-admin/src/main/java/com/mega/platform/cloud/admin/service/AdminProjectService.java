package com.mega.platform.cloud.admin.service;

import com.mega.platform.cloud.admin.dao.AdminProjectDao;
import com.mega.platform.cloud.admin.vo.AdminProjectCreateReqVO;
import com.mega.platform.cloud.admin.vo.AdminProjectListReqVO;
import com.mega.platform.cloud.admin.vo.AdminProjectEditReqVO;
import com.mega.platform.cloud.admin.vo.AdminProjectVO;
import com.mega.platform.cloud.core.PageResult;
import com.mega.platform.cloud.data.entity.Project;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 项目管理服务类
 */
@Service
@Slf4j
public class AdminProjectService {
    
    private final AdminProjectDao adminProjectDao;
    
    @Autowired
    public AdminProjectService(AdminProjectDao adminProjectDao) {
        this.adminProjectDao = adminProjectDao;
    }
    
    /**
     * 查询项目列表（不分页）
     * @param reqVO 项目列表查询参数
     * @return 项目列表
     */
    public PageResult<AdminProjectVO> findProjectList(AdminProjectListReqVO reqVO) {
        // 参数校验
        Integer pageNum = reqVO.getPageNum();
        Integer pageSize = reqVO.getPageSize();
        if (pageNum == null || pageNum < 1) {
            pageNum = 1;
        }
        if (pageSize == null || pageSize < 1) {
            pageSize = 20;
        }
        
        // 计算偏移量
        Integer offset = (pageNum - 1) * pageSize;
        
        // 1 先查询总数
        Long total = adminProjectDao.countProjectList(reqVO.getName(), reqVO.getStatus());
        
        // 2 查询列表
        List<Project> projects = adminProjectDao.selectProjectList(offset, pageSize, reqVO.getName(), reqVO.getStatus());
        
        // 转换为VO
        List<AdminProjectVO> projectVOs = projects.stream()
                .map(this::convertToVO)
                .collect(Collectors.toList());
        
        return new PageResult<>(projectVOs, total);
    }

    /**
     * 将Project实体转换为AdminProjectVO
     * @param project 项目实体
     * @return AdminProjectVO对象
     */
    private AdminProjectVO convertToVO(Project project) {
        if (project == null) {
            return null;
        }
        
        AdminProjectVO vo = new AdminProjectVO();
        BeanUtils.copyProperties(project, vo);
        
        // 设置状态描述
        vo.setStatusDesc(getStatusDesc(project.getStatus()));
        
        return vo;
    }
    
    /**
     * 根据ID获取项目详情
     * @param projectId 项目ID
     * @return 项目详情
     */
    public AdminProjectVO getProjectById(Long projectId) {
        if (projectId == null) {
            throw new IllegalArgumentException("项目ID不能为空");
        }
        
        Project project = adminProjectDao.selectProjectById(projectId);
        if (project == null) {
            throw new IllegalArgumentException("项目不存在或已删除");
        }
        
        return convertToVO(project);
    }
    
    /**
     * 创建项目
     * @param createPO 创建参数
     * @return 项目ID
     */
    @Transactional(rollbackFor = Exception.class)
    public Long createProject(AdminProjectCreateReqVO createPO) {
        // 参数校验
        if (createPO == null) {
            throw new IllegalArgumentException("创建参数不能为空");
        }
        if (!StringUtils.hasText(createPO.getName())) {
            throw new IllegalArgumentException("项目名称不能为空");
        }

        // 检查项目名称是否重复
        Project existProject = adminProjectDao.selectProjectByName(createPO.getName(), null);
        if (existProject != null) {
            throw new IllegalArgumentException("项目名称已存在");
        }
        
        // 创建项目
        Project project = new Project();
        BeanUtils.copyProperties(createPO, project);
        project.setStatus(1);
        project.setCreateTime(new Date());
        project.setUpdateTime(new Date());
        project.setDelsign((byte) 0);
        
        adminProjectDao.insertSelective(project);
        
        log.info("创建项目成功，项目ID：{}, 项目名称：{}", project.getId(), project.getName());
        return project.getId();
    }
    
    /**
     * 编辑项目
     * @param projectId 项目ID
     * @param updatePO 更新参数
     */
    @Transactional(rollbackFor = Exception.class)
    public void editProject(Long projectId, AdminProjectEditReqVO updatePO) {
        // 参数校验
        if (projectId == null) {
            throw new IllegalArgumentException("项目ID不能为空");
        }
        if (updatePO == null) {
            throw new IllegalArgumentException("更新参数不能为空");
        }
        if (!StringUtils.hasText(updatePO.getName())) {
            throw new IllegalArgumentException("项目名称不能为空");
        }
        if (updatePO.getStatus() == null) {
            throw new IllegalArgumentException("项目状态不能为空");
        }
        
        // 检查项目是否存在
        Project existProject = adminProjectDao.selectProjectById(projectId);
        if (existProject == null) {
            throw new IllegalArgumentException("项目不存在或已删除");
        }
        
        // 检查项目名称是否重复
        Project nameProject = adminProjectDao.selectProjectByName(updatePO.getName(), projectId);
        if (nameProject != null) {
            throw new IllegalArgumentException("项目名称已存在");
        }
        
        // 更新项目
        Project project = new Project();
        project.setId(projectId);
        BeanUtils.copyProperties(updatePO, project);
        project.setUpdateTime(new Date());
        
        adminProjectDao.updateByPrimaryKeySelective(project);
        
        log.info("更新项目成功，项目ID：{}, 项目名称：{}", projectId, updatePO.getName());
    }
    
    /**
     * 删除项目
     * @param projectId 项目ID
     */
    @Transactional(rollbackFor = Exception.class)
    public void deleteProject(Long projectId) {
        // 参数校验
        if (projectId == null) {
            throw new IllegalArgumentException("项目ID不能为空");
        }
        
        // 检查项目是否存在
        Project existProject = adminProjectDao.selectProjectById(projectId);
        if (existProject == null) {
            throw new IllegalArgumentException("项目不存在或已删除");
        }
        
        // 逻辑删除项目
        int result = adminProjectDao.deleteProjectById(projectId);
        if (result <= 0) {
            throw new RuntimeException("删除项目失败");
        }
        
        log.info("删除项目成功，项目ID：{}, 项目名称：{}", projectId, existProject.getName());
    }
    
    
    /**
     * 获取状态描述
     */
    private String getStatusDesc(Integer status) {
        if (status == null) {
            return "未知";
        }
        switch (status) {
            case 0:
                return "不可用";
            case 1:
                return "正常";
            case 2:
                return "挂起";
            case 3:
                return "审核中";
            default:
                return "未知";
        }
    }
}
package com.mega.platform.cloud.admin.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 权限管理-角色路由绑定更新请求参数
 */
@Data
@Accessors(chain = true)
@ApiModel("权限管理-角色路由绑定更新请求参数")
public class AdminAccessRoleRouteBindingUpdateReqVO {

    @ApiModelProperty(value = "角色ID", example = "1")
    @NotNull(message = "角色ID不能为空")
    private Long roleId;

    @ApiModelProperty(value = "路由ID列表", example = "[1,2,3]")
    @NotNull(message = "路由ID列表不能为空")
    private List<Long> routerIds;

}
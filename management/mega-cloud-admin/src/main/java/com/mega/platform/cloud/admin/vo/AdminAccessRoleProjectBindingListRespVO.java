package com.mega.platform.cloud.admin.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * 权限管理-角色项目绑定列表响应参数
 */
@Data
@Accessors(chain = true)
@ApiModel("权限管理-角色项目绑定列表响应参数")
public class AdminAccessRoleProjectBindingListRespVO {
    @ApiModelProperty(value = "角色ID", example = "1")
    private Long adminRoleId;

    // @ApiModelProperty(value = "角色名称", example = "管理员")
    // private String roleName;

    @ApiModelProperty(value = "项目ID", example = "1")
    private Long projectId;

    @ApiModelProperty(value = "项目名称", example = "示例项目")
    private String projectName;

    // @ApiModelProperty(value = "创建时间", example = "2023-01-01 12:00:00")
    // private LocalDateTime createTime;

    @ApiModelProperty(value = "删除标识(0=正常,1=删除)", example = "0")
    private Integer delsign;

}
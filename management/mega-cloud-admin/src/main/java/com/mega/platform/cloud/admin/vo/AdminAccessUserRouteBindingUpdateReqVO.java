package com.mega.platform.cloud.admin.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 权限管理-管理员路由更新绑定请求
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@ApiModel("权限管理-管理员路由更新绑定请求")
public class AdminAccessUserRouteBindingUpdateReqVO {

    @ApiModelProperty("管理员ID")
    @NotNull(message = "管理员ID不能为空")
    private Long adminUserId;

    @ApiModelProperty("路由ID")
    @NotNull(message = "路由ID") 
    private Long adminRouterId ;

    @ApiModelProperty("删除标识")
    @NotNull(message = "删除标识不能为空")
    private Integer delsign;
}
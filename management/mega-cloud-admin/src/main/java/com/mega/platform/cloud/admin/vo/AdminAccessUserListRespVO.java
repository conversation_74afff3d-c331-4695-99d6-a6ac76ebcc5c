package com.mega.platform.cloud.admin.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * 权限管理-管理员列表响应
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@ApiModel("权限管理-管理员列表响应")
public class AdminAccessUserListRespVO {

    @ApiModelProperty("管理员ID")
    private Long id;

    @ApiModelProperty("管理员用户名")
    private String username;

    @ApiModelProperty("最后登录时间")
    private Date lastLoginTime;

    @ApiModelProperty("创建时间")
    private Date createTime;

    @ApiModelProperty("删除标识: 0=未删除, 1=已删除")
    private Integer delsign;
}
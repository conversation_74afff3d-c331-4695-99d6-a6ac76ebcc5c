<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mega.platform.cloud.admin.dao.AdminScannerDao">
    
    <!-- 批量插入或更新URL模式 -->
    <insert id="batchInsertOrUpdateUrlPatterns">
        INSERT INTO project_url_pattern (name, url_pattern, remark, create_time, update_time, delsign)
        VALUES
        <foreach collection="urlPatternList" item="item" separator=",">
            (
                #{item.name},
                #{item.urlPattern},
                'FeignClient自动扫描生成',
                NOW(),
                NOW(),
                0
            )
        </foreach>
        ON DUPLICATE KEY UPDATE
            name = VALUES(name),
            update_time = NOW()
    </insert>
    
</mapper>
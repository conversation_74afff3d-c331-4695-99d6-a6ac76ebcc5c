<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mega.platform.cloud.admin.dao.AdminAppDao">
        
    <!-- 基础查询条件 -->
    <sql id="BaseWhere">
        WHERE project_id = #{projectId} AND delsign = 0
        <if test="name != null and name != ''">
            AND name LIKE CONCAT('%', #{name}, '%')
        </if>
        <if test="status != null and status != -1">
            AND status = #{status}
        </if>
    </sql>
    
    <!-- 分页查询应用列表 -->
    <select id="selectAppList" resultType="com.mega.platform.cloud.data.entity.ProjectApp">
        SELECT id, app_key, app_secret, name, project_id, status, remark, create_time, update_time, delsign
        FROM project_app
        <include refid="BaseWhere"/>
        ORDER BY create_time DESC
        LIMIT #{offset}, #{pageSize}
    </select>
    
    <!-- 查询应用总数 -->
    <select id="countAppList" resultType="java.lang.Long">
        SELECT COUNT(*)
        FROM project_app
        <include refid="BaseWhere"/>
    </select>
    
    <!-- 根据项目ID和应用ID查询应用详情 -->
    <select id="selectAppByProjectIdAndId" resultType="com.mega.platform.cloud.data.entity.ProjectApp">
        SELECT id, app_key, app_secret, name, project_id, status, remark, create_time, update_time, delsign
        FROM project_app
        WHERE project_id = #{projectId} AND id = #{id} AND delsign = 0
    </select>
    
    <!-- 根据项目ID和应用名称查询应用 -->
    <select id="selectAppByProjectIdAndName" resultType="com.mega.platform.cloud.data.entity.ProjectApp">
        SELECT id, app_key, app_secret, name, project_id, status, remark, create_time, update_time, delsign
        FROM project_app
        WHERE project_id = #{projectId} AND name = #{name} AND delsign = 0
        <if test="excludeId != null">
            AND id != #{excludeId}
        </if>
        LIMIT 1
    </select>

    <select id="countOrderList" resultType="java.lang.Long">
        SELECT COUNT(1)
        FROM payment_order po
        LEFT JOIN payment_product_config ppc ON po.payment_product_config_id = ppc.id
        WHERE po.delsign = 0
        <if test="req.orderId != null">
            AND po.id = #{req.orderId}
        </if>
        <if test="req.parentId != null">
            AND (po.parent_payment_order_id = #{req.parentId} OR po.id = #{req.parentId})
        </if>
        <if test="req.platformCode != null and req.platformCode != ''">
            AND ppc.platform_code = #{req.platformCode}
        </if>
        <if test="req.orderType != null and req.orderType != ''">
            AND ppc.order_type = #{req.orderType}
        </if>
        <if test="req.status != null">
            AND po.status = #{req.status}
        </if>
        <if test="req.payTimeStart != null">
            AND po.pay_time &gt;= #{req.payTimeStart}
        </if>
        <if test="req.payTimeEnd != null">
            AND po.pay_time &lt;= #{req.payTimeEnd}
        </if>
    </select>

    <select id="selectOrderList" resultType="com.mega.platform.cloud.admin.dto.PaymentOrderDTO">
        SELECT po.*, tp.platform_code
        FROM payment_order po
        LEFT JOIN payment_product_config ppc ON po.payment_product_config_id = ppc.id
        LEFT JOIN third_platform tp ON tp.id = po.third_platform_id
        WHERE po.delsign = 0
        <if test="req.orderId != null">
            AND po.id = #{req.orderId}
        </if>
        <if test="req.parentId != null">
            AND (po.parent_payment_order_id = #{req.parentId} OR po.id = #{req.parentId})
        </if>
        <if test="req.platformCode != null and req.platformCode != ''">
            AND tp.platform_code = #{req.platformCode}
        </if>
        <if test="req.isSubscription != null">
            AND ppc.is_subscription = #{req.isSubscription}
        </if>
        <if test="req.status != null">
            AND po.status = #{req.status}
        </if>
        <if test="req.payTimeStart != null">
            AND po.pay_time &gt;= #{req.payTimeStart}
        </if>
        <if test="req.payTimeEnd != null">
            AND po.pay_time &lt;= #{req.payTimeEnd}
        </if>
        ORDER BY po.id DESC
        LIMIT #{limit} OFFSET #{offset}
    </select>

    <select id="selectParentId" resultType="java.lang.Long">
        SELECT CASE
        WHEN parent_payment_order_id IS NULL THEN id
        ELSE parent_payment_order_id
        END AS root_id
        FROM payment_order
        WHERE id = #{orderId};
    </select>

    <select id="selectSubscriptionPage" resultType="com.mega.platform.cloud.admin.vo.AdminPaymentSubscriptionRespVO">
        SELECT ps.*, tp.platform_code
        FROM payment_subscription ps
        LEFT JOIN payment_product_config ppc ON ps.payment_product_config_id = ppc.id
        LEFT JOIN third_platform tp ON tp.id = ps.third_platform_id
        WHERE ps.delsign = 0
        <if test="parentId != null">
            AND ps.payment_order_id = #{parentId}
        </if>
        <if test="req.platformCode != null and req.platformCode != ''">
            AND ps.platform_code = #{req.platformCode}
        </if>
        <if test="req.status != null">
            AND ps.status = #{req.status}
        </if>
        ORDER BY ps.create_time DESC
        LIMIT #{offset}, #{limit}
    </select>

    <select id="selectSubscriptionCount" resultType="java.lang.Long">
        SELECT COUNT(1)
        FROM payment_subscription ps
        WHERE ps.delsign = 0
        <if test="parentId != null">
            AND ps.payment_order_id = #{parentId}
        </if>
        <if test="req.platformCode != null and req.platformCode != ''">
            AND ps.platform_code = #{req.platformCode}
        </if>
        <if test="req.status != null">
            AND ps.status = #{req.status}
        </if>
    </select>

    <select id="selectSubscriptionChargeLogPage"
            resultType="com.mega.platform.cloud.data.entity.PaymentSubscriptionChargeLog">
        SELECT pscl.*
        FROM payment_subscription_charge_log pscl
        LEFT JOIN payment_order po ON po.id = pscl.payment_order_id
        WHERE pscl.delsign = 0
        <if test="parentId != null">
            AND po.parent_payment_order_id = #{parentId}
        </if>
        <if test="req.paymentSubscriptionId != null">
            AND pscl.payment_subscrition_id = #{req.paymentSubscriptionId}
        </if>
        ORDER BY pscl.create_time DESC
        LIMIT #{offset}, #{limit}
    </select>

    <select id="selectSubscriptionChargeLogCount" resultType="java.lang.Long">
        SELECT COUNT(1)
        FROM payment_subscription_charge_log pscl
        LEFT JOIN payment_order po ON po.id = pscl.payment_order_id
        WHERE pscl.delsign = 0
        <if test="parentId != null">
            AND po.parent_payment_order_id = #{parentId}
        </if>
        <if test="req.paymentSubscriptionId != null">
            AND pscl.payment_subscrition_id = #{req.paymentSubscriptionId}
        </if>
    </select>

    <select id="selectPaymentCallbackLogPage"
            resultType="com.mega.platform.cloud.data.entity.PaymentCallbackLog">
        SELECT pcl.*
        FROM payment_callback_log pcl
        LEFT JOIN payment_order po ON po.id = pcl.payment_order_id
        WHERE pcl.delsign = 0
        <if test="parentId != null">
            AND po.parent_payment_order_id = #{parentId}
        </if>
        <if test="req.platformCode != null">
            AND pcl.platform_code = #{req.platformCode}
        </if>
        ORDER BY pcl.create_time DESC
        LIMIT #{offset}, #{limit}
    </select>

    <select id="selectPaymentCallbackLoCount" resultType="java.lang.Long">
        SELECT COUNT(1)
        FROM payment_callback_log pcl
        LEFT JOIN payment_order po ON po.id = pcl.payment_order_id
        WHERE pcl.delsign = 0
        <if test="parentId != null">
            AND po.parent_payment_order_id = #{parentId}
        </if>
        <if test="req.platformCode != null">
            AND pcl.platform_code = #{req.platformCode}
        </if>
    </select>

</mapper>
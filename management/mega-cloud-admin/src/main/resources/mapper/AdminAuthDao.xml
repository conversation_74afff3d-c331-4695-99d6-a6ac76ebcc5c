<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mega.platform.cloud.admin.dao.AdminAuthDao">

    <!-- 根据用户名查询管理员用户 -->
    <select id="findByUsername" resultType="com.mega.platform.cloud.data.entity.AdminUser">
        SELECT id, username, password, last_login_time, create_time, update_time, delsign
        FROM admin_user
        WHERE username = #{username}
          AND delsign = 0
    </select>

    <!-- 根据用户ID查询管理员用户 -->
    <select id="findById" resultType="com.mega.platform.cloud.data.entity.AdminUser">
        SELECT id, username, password, last_login_time, create_time, update_time, delsign
        FROM admin_user
        WHERE id = #{adminUserId}
          AND delsign = 0
    </select>

    <!-- 根据管理员用户ID查询用户角色列表 -->
    <select id="findRolesByAdminUserId" resultType="com.mega.platform.cloud.data.entity.AdminRole">
        SELECT r.id, r.name, r.description, r.create_time, r.update_time, r.delsign
        FROM admin_role r
        INNER JOIN admin_user_role_binding urb ON r.id = urb.admin_role_id
        WHERE urb.admin_user_id = #{adminUserId}
          AND r.delsign = 0
    </select>

    <!-- 根据角色ID列表查询路由权限列表 -->
    <select id="findRoutersByRoleIds" resultType="com.mega.platform.cloud.data.entity.AdminRouter">
        SELECT DISTINCT rt.id, rt.backend_path, rt.frontend_path, rt.frontend_name, rt.description, 
               rt.parent_admin_router_id, rt.create_time, rt.update_time, rt.delsign, rt.router_cate
        FROM admin_router rt
        WHERE 1=1
        <choose>
            <!-- 如果包含超级管理员角色ID 1，返回所有路由 -->
            <when test="roleIds != null and roleIds.contains(1L)">
                AND rt.delsign = 0
            </when>
            <!-- 否则返回角色绑定的路由 -->
            <otherwise>
                AND EXISTS (
                    SELECT 1 FROM admin_role_router_binding rrb 
                    WHERE rt.id = rrb.admin_router_id 
                    AND rrb.admin_role_id IN
                    <foreach collection="roleIds" item="roleId" open="(" separator="," close=")">
                        #{roleId}
                    </foreach>
                    AND rrb.delsign = 0
                )
                AND rt.delsign = 0
            </otherwise>
        </choose>
    </select>

    <!-- 根据管理员用户ID查询直接分配的路由权限列表 -->
    <select id="findRoutersByAdminUserId" resultType="com.mega.platform.cloud.data.entity.AdminRouter">
        SELECT rt.id, rt.backend_path, rt.frontend_path, rt.description, 
               rt.parent_admin_router_id, rt.create_time, rt.update_time, rt.delsign, rt.router_cate
        FROM admin_router rt
        INNER JOIN admin_user_router_binding urb ON rt.id = urb.admin_router_id
        WHERE urb.admin_user_id = #{adminUserId}
          AND rt.delsign = 0
          AND urb.delsign = 0
    </select>

    <!-- 根据角色ID列表查询关联的项目列表 -->
    <select id="findProjectsByRoleIds" resultType="com.mega.platform.cloud.data.entity.Project">
        SELECT DISTINCT p.id, p.name, p.remark, p.create_time, p.update_time, p.delsign
        FROM project p
        <choose>
            <!-- 如果包含超级管理员角色ID 1，返回所有项目 -->
            <when test="roleIds != null and roleIds.contains(1L)">
                WHERE p.delsign = 0
            </when>
            <!-- 否则返回角色绑定的项目 -->
            <otherwise>
                INNER JOIN admin_role_project_binding rpb ON p.id = rpb.project_id
                WHERE rpb.admin_role_id IN
                <foreach collection="roleIds" item="roleId" open="(" separator="," close=")">
                    #{roleId}
                </foreach>
                AND p.delsign = 0
                AND rpb.delsign = 0
            </otherwise>
        </choose>
    </select>

    <!-- 根据管理员用户ID查询直接分配的项目列表 -->
    <select id="findProjectsByAdminUserId" resultType="com.mega.platform.cloud.data.entity.Project">
        SELECT p.id, p.name, p.remark, p.create_time, p.update_time, p.delsign
        FROM project p
        INNER JOIN admin_user_project_binding upb ON p.id = upb.project_id
        WHERE upb.admin_user_id = #{adminUserId}
          AND p.delsign = 0
          AND upb.delsign = 0
    </select>

    <!-- 更新管理员用户最后登录时间 -->
    <update id="updateLastLoginTime">
        UPDATE admin_user
        SET last_login_time = NOW()
        WHERE id = #{adminUserId}
    </update>

</mapper>
# Mega Cloud 项目结构规范

## 1. 项目架构概述

Mega Cloud 采用分层微服务架构，遵循清晰的关注点分离原则。

## 2. 模块组织结构

### 2.1 基础模块 (`base/`)
- **mega-cloud-common**: 通用共享模块，包含工具类、常量和通用功能
- **mega-cloud-core**: 核心基础模块，包含领域核心逻辑和基础业务能力
- **mega-cloud-data**: 数据模块，提供公共的 DTO、Entity、VO 数据结构
- **mega-cloud-client**: Feign 客户端接口，用于服务间通信
- **mega-cloud-gateway**: API 网关和路由管理
- **mega-cloud-generator**: 代码生成模块，包含代码生成工具和模板

### 2.2 管理模块 (`management/`)
- **mega-cloud-admin**: 后台管理模块，包含后台管理系统和业务逻辑
- **mega-cloud-access**: 访问控制模块，负责认证和授权服务
- **mega-cloud-monitor**: 监控模块，包含服务监控、健康检查和日志记录

### 2.3 第三方集成模块 (`third-party/`)
- **mega-cloud-auth**: 认证授权模块，外部认证提供商集成
- **mega-cloud-payment**: 支付模块，支付网关集成（微信、支付宝）
- **mega-cloud-push**: 推送服务模块，通知服务（短信、邮件、推送）

### 2.4 微服务模块 (`microservice/`)
- **mega-cloud-microservice**: 微服务框架模块，主要微服务编排和协调

## 3. API 路由规范

### 3.1 Admin API 路由
- `/admin/api/public/{controller}/xxx` - 公开端点（无需认证，如登录接口）
- `/admin/api/system/{controller}/xxx` - 系统级权限接口（仅需 token 验证）
- `/admin/api/{project_id}/{controller}/xxx` - 项目级权限接口（token + 项目访问权限）

### 3.2 应用 API 路由
- `/{module-name}/api/**/public/**` - 公开端点（无需 token 验证）

## 4. 认证头规范
- 使用 `adminToken` 头进行管理员认证（不使用 `Authorization: Bearer`）
- JWT token 仅包含 `adminUserId` 和 `tokenVersion`
- 用户权限和项目列表缓存在 Redis 中

## 5. 文件命名约定
- **Controllers**: `{Module}{Entity}Controller.java`
- **Services**: `{Module}{Entity}Service.java`
- **VOs**: `{Module}{Entity}{Action}ReqVO.java` / `{Module}{Entity}{Action}RespVO.java`
- 使用 "router" 术语（不是 "route"）表示前端路由概念

## 6. 配置文件结构
- **主配置**: `pom.xml`（根级别）
- **模块配置**: `{module}/pom.xml`
- **MyBatis 生成器**: `src/main/resources/generatorConfig.xml`
- **部署脚本**: `mega-cloud.sh`

## 7. 文档结构
- `docs/admin/` - 管理文档
- `docs/sql/` - 数据库模式和迁移
- `docs/third/` - 第三方集成指南
- `README.md` - 项目概述和 API 文档
- `TODO.md` - 开发任务和进度跟踪

## 8. 目录结构示例

```
mega-cloud/
├── base/
│   ├── mega-cloud-common/
│   ├── mega-cloud-core/
│   ├── mega-cloud-data/
│   ├── mega-cloud-client/
│   ├── mega-cloud-gateway/
│   └── mega-cloud-generator/
├── management/
│   ├── mega-cloud-admin/
│   ├── mega-cloud-access/
│   └── mega-cloud-monitor/
├── third-party/
│   ├── mega-cloud-auth/
│   ├── mega-cloud-payment/
│   └── mega-cloud-push/
├── microservice/
│   └── mega-cloud-microservice/
├── docs/
│   ├── admin/
│   ├── sql/
│   └── third/
├── pom.xml
├── README.md
├── TODO.md
└── mega-cloud.sh
```

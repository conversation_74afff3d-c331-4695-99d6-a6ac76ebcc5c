package com.mega.platform.cloud.auth.client;

import com.auth0.jwt.JWT;
import com.auth0.jwt.interfaces.DecodedJWT;
import com.mega.platform.cloud.auth.cache.AuthAppConfigCache;
import com.mega.platform.cloud.common.enums.ThirdPlatformEnum;
import com.mega.platform.cloud.data.entity.AuthAliyunSmsConfig;
import com.mega.platform.cloud.data.entity.AuthAppleConfig;
import com.nimbusds.jose.jwk.JWK;
import com.nimbusds.jose.jwk.JWKSet;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.io.InputStream;
import java.net.URL;
import java.security.interfaces.RSAPublicKey;

@Slf4j
@RequiredArgsConstructor
@Component
public class AppleTokenVerifierClient {
    private static final String APPLE_PUBLIC_KEYS_URL = "https://appleid.apple.com/auth/keys";
    private static final String APPLE_ISSUER = "https://appleid.apple.com";
    private final AuthAppConfigCache configCache;

    /**
     * 校验 id_token 并提取用户 sub（Apple 的唯一标识）
     */
    public String verifyAndExtractSub(Long projectAppId, String idToken) {
        AuthAppleConfig config = configCache.getTypedConfig(projectAppId, ThirdPlatformEnum.APPLE.getCode(), AuthAppleConfig.class);

        try {
            DecodedJWT jwt = JWT.decode(idToken);
            String kid = jwt.getKeyId();
            String alg = jwt.getAlgorithm();

            if (!"RS256".equals(alg)) {
                throw new IllegalArgumentException("Unsupported JWT algorithm: " + alg);
            }

            RSAPublicKey publicKey = getApplePublicKey(kid);
            if (publicKey == null) {
                throw new IllegalArgumentException("No public key found for kid: " + kid);
            }

            // 手动验证关键字段
            JWT.require(com.auth0.jwt.algorithms.Algorithm.RSA256(publicKey, null))
                    .withIssuer(APPLE_ISSUER)
                    .withAudience(config.getAud())
                    .build()
                    .verify(idToken);

            // 签名验证通过，返回 sub
            return jwt.getSubject();

        } catch (Exception e) {
            log.error("Apple ID token verification failed", e);
            throw new RuntimeException("Apple ID token verification failed", e);
        }
    }

    /**
     * 从 Apple 公钥列表中提取指定 kid 的 RSAPublicKey
     */
    private RSAPublicKey getApplePublicKey(String kid) throws Exception {
        InputStream inputStream = new URL(APPLE_PUBLIC_KEYS_URL).openStream();
        JWKSet jwkSet = JWKSet.load(inputStream);

        for (JWK jwk : jwkSet.getKeys()) {
            if (jwk.getKeyID().equals(kid)) {
                return (RSAPublicKey) jwk.toRSAKey().toPublicKey();
            }
        }
        return null;
    }
}

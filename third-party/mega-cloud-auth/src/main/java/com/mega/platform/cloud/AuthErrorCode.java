package com.mega.platform.cloud;

import com.mega.platform.cloud.core.utils.SpringUtils;
import lombok.Getter;

@Getter
public enum AuthErrorCode {

    ERR_0(0),
    ;

    private final Integer code;

    AuthErrorCode(Integer code) {
        this.code = code;
    }

    public static AuthErrorCode getExchangeCode(Integer code) {
        for (AuthErrorCode exchangeCode : AuthErrorCode.values()) {
            if (exchangeCode.getCode().equals(code)) {
                return exchangeCode;
            }
        }
        return null;
    }

    public String getMessage() {
        return SpringUtils.getLocaleMessage(this.code);
    }
}

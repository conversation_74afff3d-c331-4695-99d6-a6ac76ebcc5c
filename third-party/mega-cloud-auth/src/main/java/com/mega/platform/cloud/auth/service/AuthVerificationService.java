package com.mega.platform.cloud.auth.service;

import com.mega.platform.cloud.AuthErrorCode;
import com.mega.platform.cloud.AuthException;
import com.mega.platform.cloud.auth.client.AppleTokenVerifierClient;
import com.mega.platform.cloud.auth.client.WeChatVerifierClient;
import com.mega.platform.cloud.common.enums.ThirdPlatformEnum;
import com.mega.platform.cloud.common.mapper.AuthVerifyLogMapper;
import com.mega.platform.cloud.data.entity.AuthVerifyLog;
import com.mega.platform.cloud.data.entity.AuthWeChatAccessTokenResp;
import com.mega.platform.cloud.data.entity.AuthWeChatUserInfoResp;
import com.mega.platform.cloud.data.vo.auth.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor
public class AuthVerificationService {
    private final AuthSmsService authSmsService;
    private final AuthVerifyLogMapper authVerifyLogMapper;
    private final AppleTokenVerifierClient appleTokenVerifierClient;
    private final WeChatVerifierClient weChatVerifierClient;
    public AuthSendSmsCodeRespVO sendSmsCode(AuthSendSmsCodeReqVO vo) throws Exception {
        AuthSendSmsCodeRespVO respVO = new AuthSendSmsCodeRespVO();
        String code = authSmsService.sendAndCachePhoneCode(vo.getAppId(), vo.getPhoneNum(), vo.getAreaCode(), vo.getType());
        respVO.setCode(code);
        return respVO;
    }

    public boolean verifySmsCode(AuthVerifySmsCodeReqVO vo) {
        boolean verifyResult = authSmsService.verifyPhoneCode(vo.getAppId(), vo.getPhoneNum(), vo.getCode());
        AuthVerifyLog authVerifyLog = new AuthVerifyLog();
        authVerifyLog.setProjectAppId(vo.getAppId()).setChannelCode(ThirdPlatformEnum.ALIPAY.getPlatformCode())
                .setLoginId(vo.getPhoneNum()).setVerifyResult(verifyResult?"success":"fail")
                .setDeviceId(vo.getDeviceId()).setClientIp(vo.getClientIp());
        if (!verifyResult) {
            authVerifyLog.setErrorMsg("验证码不存在");
        }
        authVerifyLogMapper.insertSelective(authVerifyLog);
        return verifyResult;
    }

    public AuthVerifyAppleTokenRespVO verifyAppleToken(AuthVerifyAppleTokenReqVO vo) {
        AuthVerifyAppleTokenRespVO respVO = new AuthVerifyAppleTokenRespVO();
        AuthVerifyLog authVerifyLog = new AuthVerifyLog();
        authVerifyLog.setProjectAppId(vo.getAppId()).setChannelCode(ThirdPlatformEnum.APPLE.getPlatformCode())
                .setDeviceId(vo.getDeviceId()).setClientIp(vo.getClientIp());
        try {
            String subject = appleTokenVerifierClient.verifyAndExtractSub(vo.getAppId(), vo.getIdToken());
            respVO.setSubject(subject);
            respVO.setSuccess(true);
            authVerifyLog.setLoginId(subject).setVerifyResult("success");
        } catch (Exception e) {
            authVerifyLog.setVerifyResult("fail").setErrorMsg(e.getMessage());
            respVO.setSuccess(false);
        }
        authVerifyLogMapper.insertSelective(authVerifyLog);
        return respVO;
    }

    public AuthWeChatUserInfoRespVO verifyWeChatCode(AuthWeChatUserInfoReqVO vo) {
        AuthWeChatUserInfoRespVO respVO = new AuthWeChatUserInfoRespVO();
        AuthWeChatAccessTokenResp tokenResp = weChatVerifierClient.getAccessToken(vo.getAppId(), vo.getCode());
        AuthVerifyLog authVerifyLog = new AuthVerifyLog();
        authVerifyLog.setProjectAppId(vo.getAppId()).setChannelCode(ThirdPlatformEnum.APPLE.getPlatformCode())
                .setDeviceId(vo.getDeviceId()).setClientIp(vo.getClientIp());
        if (tokenResp.getErrcode() != null) {
            authVerifyLog.setVerifyResult("fail").setErrorMsg(tokenResp.getErrmsg());
            respVO.setSuccess(false);
        } else {
            authVerifyLog.setLoginId(tokenResp.getUnionid()).setVerifyResult("success");
            respVO.setSuccess(true);
        }

        AuthWeChatUserInfoResp userInfo = weChatVerifierClient.getUserInfo(tokenResp.getAccessToken(), tokenResp.getOpenid());
        BeanUtils.copyProperties(userInfo, respVO);
        return respVO;
    }
}

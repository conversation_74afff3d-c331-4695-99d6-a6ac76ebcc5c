package com.mega.platform.cloud.auth.controller;

import com.mega.platform.cloud.auth.service.AuthVerificationService;
import com.mega.platform.cloud.client.auth.AuthVerificationClient;
import com.mega.platform.cloud.common.utils.EnvUtil;
import com.mega.platform.cloud.core.Result;
import com.mega.platform.cloud.core.Results;
import com.mega.platform.cloud.data.vo.auth.*;
import io.swagger.annotations.Api;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequiredArgsConstructor
@Api(tags = "auth验证接口")
public class AuthVerificationController implements AuthVerificationClient {
    private final AuthVerificationService authVerificationService;
    @Override
    public Result<AuthSendSmsCodeRespVO> sendSmsCode(AuthSendSmsCodeReqVO vo) throws Exception {
        return Results.success(authVerificationService.sendSmsCode(vo));
    }

    @Override
    public Result<Boolean> verifySmsCode(AuthVerifySmsCodeReqVO vo) {
        return Results.success(authVerificationService.verifySmsCode(vo));
    }

    @Override
    public Result<AuthVerifyAppleTokenRespVO> verifyAppleToken(AuthVerifyAppleTokenReqVO vo) {
        return Results.success(authVerificationService.verifyAppleToken(vo));
    }

    @Override
    public Result<AuthWeChatUserInfoRespVO> verifyWechatCode(AuthWeChatUserInfoReqVO vo) {
        return Results.success(authVerificationService.verifyWeChatCode(vo));
    }


}

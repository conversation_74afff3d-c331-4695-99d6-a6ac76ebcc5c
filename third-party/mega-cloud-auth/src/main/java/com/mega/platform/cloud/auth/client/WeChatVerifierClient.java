package com.mega.platform.cloud.auth.client;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.mega.platform.cloud.auth.cache.AuthAppConfigCache;
import com.mega.platform.cloud.common.enums.ThirdPlatformEnum;
import com.mega.platform.cloud.data.entity.AuthWeChatConfig;
import com.mega.platform.cloud.data.entity.AuthWeChatAccessTokenResp;
import com.mega.platform.cloud.data.entity.AuthWeChatUserInfoResp;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.converter.StringHttpMessageConverter;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import java.nio.charset.StandardCharsets;

@Component
@RequiredArgsConstructor
@Slf4j
public class WeChatVerifierClient {
    private final AuthAppConfigCache configCache;

    private static final String ACCESS_TOKEN_URL =
            "https://api.weixin.qq.com/sns/oauth2/access_token?appid=%s&secret=%s&code=%s&grant_type=authorization_code";

    private static final String USER_INFO_URL =
            "https://api.weixin.qq.com/sns/userinfo?access_token=%s&openid=%s";

    private final ObjectMapper objectMapper = new ObjectMapper();

    /**
     * 获取微信 access_token
     */
    public AuthWeChatAccessTokenResp getAccessToken(Long appId, String code) {
        AuthWeChatConfig config = configCache.getTypedConfig(appId, ThirdPlatformEnum.WECHAT.getCode(), AuthWeChatConfig.class);

        String url = String.format(ACCESS_TOKEN_URL, config.getAppId(), config.getAppSecret(), code);

        String response = new RestTemplate().getForObject(url, String.class);
        log.info("response:" + response);
        try {
            return objectMapper.readValue(response, AuthWeChatAccessTokenResp.class);
        } catch (Exception e) {
            throw new RuntimeException("解析微信 access_token 响应失败：" + response, e);
        }
    }

    /**
     * 获取微信用户信息
     */
    public AuthWeChatUserInfoResp getUserInfo(String accessToken, String openid) {
        String url = String.format(USER_INFO_URL, accessToken, openid);

        RestTemplate restTemplate = new RestTemplate();
        restTemplate.getMessageConverters().stream()
                .filter(converter -> converter instanceof StringHttpMessageConverter)
                .forEach(converter -> ((StringHttpMessageConverter) converter).setDefaultCharset(StandardCharsets.UTF_8));

        String response = restTemplate.getForObject(url, String.class);
        try {
            return objectMapper.readValue(response, AuthWeChatUserInfoResp.class);
        } catch (Exception e) {
            throw new RuntimeException("解析微信用户信息响应失败：" + response, e);
        }
    }
}

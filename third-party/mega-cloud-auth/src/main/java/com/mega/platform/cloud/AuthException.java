package com.mega.platform.cloud;


import com.mega.platform.cloud.core.exception.BaseException;

import java.util.Objects;

public class AuthException extends BaseException {

    public AuthException(Integer code) {
        this(Objects.requireNonNull(AuthErrorCode.getExchangeCode(code)));
    }

    public AuthException(AuthErrorCode code) {
        super(code.getCode(), code.getMessage());
    }

    public AuthException(Integer code, String message) {
        super(code, message);
    }
}

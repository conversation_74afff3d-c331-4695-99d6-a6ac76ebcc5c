package com.mega.platform.cloud.auth.service;

import com.mega.platform.cloud.AuthErrorCode;
import com.mega.platform.cloud.AuthException;
import com.mega.platform.cloud.auth.cache.AuthAppConfigCache;
import com.mega.platform.cloud.auth.client.AliyunSmsClient;
import com.mega.platform.cloud.data.entity.AuthAliyunSmsConfig;
import com.mega.platform.cloud.auth.util.SmsKeyUtil;
import com.mega.platform.cloud.common.enums.ThirdPlatformEnum;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.util.concurrent.TimeUnit;

@Service
@Slf4j
@RequiredArgsConstructor
public class AuthSmsService {
    private final StringRedisTemplate stringRedisTemplate0;
    private final AuthAppConfigCache configCache;
    private final AliyunSmsClient aliyunSmsClient;

    private static final int MAX_SEND_LIMIT = 5;

    /**
     * 发送短信验证码并写入 Redis，用于登录、注册、绑定手机等场景。
     *
     * @param appId     应用ID
     * @param phone     手机号（不带区号）
     * @param areaCode  手机区号（如 +86）
     * @param type      验证码用途类型（如 login、register、reset_pwd、bind_phone）
     * @return          生成的验证码
     * @throws Exception 短信服务异常或超过发送限制
     */
    public String sendAndCachePhoneCode(Long appId, String phone, String areaCode, String type) throws Exception {
        String limitKey = SmsKeyUtil.phoneLimitKey(appId, phone);
        String codeKey = SmsKeyUtil.verificationCodeKey(appId, phone);
        // 每天最多发送次数判断
        String countStr = stringRedisTemplate0.opsForValue().get(limitKey);
        if (countStr != null && Integer.parseInt(countStr) >= MAX_SEND_LIMIT) {
            throw new AuthException(AuthErrorCode.ERR_0); // 超过发送次数
        }

        // 生成6位验证码
        String code = RandomStringUtils.randomNumeric(6);

        // 发送短信
        aliyunSmsClient.sendSms(appId, areaCode, phone, type, code);

        // 写入 Redis：验证码缓存 + 发送次数统计
        int expireSecond = getExpireSecond(appId);
        stringRedisTemplate0.opsForValue().set(codeKey, code, expireSecond, TimeUnit.SECONDS);
        stringRedisTemplate0.opsForValue().increment(limitKey);
        stringRedisTemplate0.expire(limitKey, 1, TimeUnit.DAYS);

        return code;
    }


    private int getExpireSecond(Long appId) {
        AuthAliyunSmsConfig config = configCache.getTypedConfig(appId, ThirdPlatformEnum.ALIPAY.getCode(), AuthAliyunSmsConfig.class);
        return config.getCodeValidSecond();
    }

    /**
     * 校验手机验证码是否正确
     * @param appId 应用ID
     * @param phone 手机号
     * @param inputCode 用户输入的验证码
     * @return 是否验证成功
     */
    public boolean verifyPhoneCode(Long appId, String phone, String inputCode) {
        String redisKey = SmsKeyUtil.verificationCodeKey(appId, phone);
        String cachedCode = stringRedisTemplate0.opsForValue().get(redisKey);

        // 不存在或已过期
        if (StringUtils.isBlank(cachedCode)) {
            return false;
        }

        // 匹配
        boolean isMatch = cachedCode.equals(inputCode);

        // 验证成功即删除（防止重放攻击）
        if (isMatch) {
            stringRedisTemplate0.delete(redisKey);
        }

        return isMatch;
    }


}

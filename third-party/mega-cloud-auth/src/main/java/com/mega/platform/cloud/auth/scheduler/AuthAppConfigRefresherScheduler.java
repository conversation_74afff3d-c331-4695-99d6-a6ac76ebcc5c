package com.mega.platform.cloud.auth.scheduler;

import com.mega.platform.cloud.auth.cache.AuthAppConfigCache;
import com.mega.platform.cloud.data.entity.AuthAppConfig;
import com.mega.platform.cloud.common.mapper.AuthAppConfigMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@Slf4j
@RequiredArgsConstructor
public class AuthAppConfigRefresherScheduler {
    private final AuthAppConfigMapper configMapper;
    private final AuthAppConfigCache configCache;

    @Scheduled(fixedDelay = 5 * 60 * 1000) // 每 5 分钟刷新
    public void refresh() {
        List<AuthAppConfig> list = configMapper.select(new AuthAppConfig().setDelsign((byte) 0));
        configCache.update(list);
        log.info("刷新 auth_app_config 缓存，共加载 {} 条记录", list.size());
    }
}

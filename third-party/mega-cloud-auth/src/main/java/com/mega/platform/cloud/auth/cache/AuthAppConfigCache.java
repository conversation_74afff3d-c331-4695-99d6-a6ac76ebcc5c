package com.mega.platform.cloud.auth.cache;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.mega.platform.cloud.data.entity.AuthAppConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

@Component
@Slf4j
public class AuthAppConfigCache {
    private final Map<String, JsonNode> configMap = new ConcurrentHashMap<>();

    public void update(List<AuthAppConfig> list) {
        Map<String, JsonNode> temp = new HashMap<>();
        ObjectMapper objectMapper = new ObjectMapper();

        for (AuthAppConfig item : list) {
            try {
                JsonNode jsonNode = objectMapper.readTree(item.getConfig());
                String key = generateKey(item.getProjectAppId(), item.getThirdPlatformId());
                temp.put(key, jsonNode);
            } catch (Exception e) {
                log.warn("解析配置失败，id={}, error={}", item.getId(), e.getMessage());
            }
        }

        synchronized (this) {
            configMap.clear();
            configMap.putAll(temp);
        }
    }

    public JsonNode getRawConfig(Long projectAppId, Long thirdPlatformId) {
        return configMap.get(generateKey(projectAppId, thirdPlatformId));
    }

    public <T> T getTypedConfig(Long projectAppId, Long thirdPlatformId, Class<T> clazz) {
        JsonNode jsonNode = getRawConfig(projectAppId, thirdPlatformId);
        if (jsonNode == null) return null;
        return new ObjectMapper().convertValue(jsonNode, clazz);
    }

    private String generateKey(Long projectAppId, Long thirdPlatformId) {
        return projectAppId + ":" + thirdPlatformId;
    }
}

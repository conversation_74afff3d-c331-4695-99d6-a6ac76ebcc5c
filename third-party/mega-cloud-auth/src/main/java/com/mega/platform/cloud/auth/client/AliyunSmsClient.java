package com.mega.platform.cloud.auth.client;

import com.aliyun.dysmsapi20170525.models.SendSmsRequest;
import com.aliyun.teaopenapi.models.Config;
import com.aliyun.teautil.models.RuntimeOptions;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.mega.platform.cloud.auth.cache.AuthAppConfigCache;
import com.mega.platform.cloud.data.entity.AuthAliyunSmsConfig;
import com.mega.platform.cloud.common.enums.ThirdPlatformEnum;
import com.mega.platform.cloud.common.mapper.AuthSmsTemplateConfigMapper;
import com.mega.platform.cloud.data.entity.AuthSmsTemplateConfig;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

@Component
@RequiredArgsConstructor
public class AliyunSmsClient {
    private final AuthAppConfigCache configCache;
    private final AuthSmsTemplateConfigMapper templateMapper;

    public void sendSms(Long appId, String areaCode, String phone, String type, String code) throws Exception {
        AuthAliyunSmsConfig config = configCache.getTypedConfig(appId, ThirdPlatformEnum.ALIPAY.getCode(), AuthAliyunSmsConfig.class);
        String fullPhone = (areaCode != null ? areaCode.replace("+", "") : "") + phone;

        AuthSmsTemplateConfig template = templateMapper.selectOne(
                new AuthSmsTemplateConfig()
                        .setType(type)
                        .setProjectAppId(appId)
                        .setThirdPlatformId(ThirdPlatformEnum.ALIPAY.getCode())
                        .setDelsign((byte) 0)
        );
        Map<String, String> paramMap = new HashMap<>();
        paramMap.put("code", code);

        ObjectMapper objectMapper = new ObjectMapper();
        String templateParamJson = objectMapper.writeValueAsString(paramMap);

        SendSmsRequest request = new SendSmsRequest()
                .setPhoneNumbers(fullPhone)
                .setSignName(config.getSignName())
                .setTemplateCode(template.getSmsTemplateCode())
                .setTemplateParam(templateParamJson);

        createClient(config).sendSmsWithOptions(request, new RuntimeOptions());
    }

    private com.aliyun.dysmsapi20170525.Client createClient(AuthAliyunSmsConfig config) throws Exception {
        Config sdkConfig = new Config()
                .setAccessKeyId(config.getAccessKeyId())
                .setAccessKeySecret(config.getAccessKeySecret());
        sdkConfig.endpoint = config.getEndpoint();
        return new com.aliyun.dysmsapi20170525.Client(sdkConfig);
    }
}

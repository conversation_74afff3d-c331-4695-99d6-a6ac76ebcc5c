package com.mega.platform.cloud.payment.util;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Random;

public class OrderNoUtil {
    private static final SimpleDateFormat TIME_FORMAT = new SimpleDateFormat("yyyyMMddHHmmss");
    private static final Random RANDOM = new Random();

    /**
     * 基本订单号（带时间戳+随机数）
     * 示例：ALP202507301015321238
     */
    public static String generate(String prefix) {
        String timestamp = TIME_FORMAT.format(new Date());
        int random = 100000 + RANDOM.nextInt(900000); // 6位随机数
        return prefix + timestamp + random;
    }
}

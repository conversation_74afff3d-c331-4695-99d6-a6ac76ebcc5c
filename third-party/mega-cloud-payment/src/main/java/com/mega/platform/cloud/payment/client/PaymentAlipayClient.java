package com.mega.platform.cloud.payment.client;

import com.alipay.easysdk.factory.Factory;
import com.alipay.easysdk.kernel.Config;
import com.alipay.easysdk.kernel.util.ResponseChecker;
import com.alipay.easysdk.payment.app.models.AlipayTradeAppPayResponse;
import com.alipay.easysdk.payment.page.models.AlipayTradePagePayResponse;
import com.mega.platform.cloud.common.enums.ThirdPlatformEnum;
import com.mega.platform.cloud.data.entity.PaymentAlipayConfig;
import com.mega.platform.cloud.payment.cache.PaymentAppConfigCache;
import com.mega.platform.cloud.payment.entity.PaymentAlipayClientResult;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Map;

@Component
@RequiredArgsConstructor
@Slf4j
public class PaymentAlipayClient {
    private final PaymentAppConfigCache paymentAppConfigCache;

    /**
     * 创建支付宝交易（APP支付场景）
     */
    public PaymentAlipayClientResult createAlipayTransaction(String outTradeNo, String totalAmount, String subject, Long appId) {
        PaymentAlipayClientResult result = new PaymentAlipayClientResult();
        try {
            PaymentAlipayConfig config = paymentAppConfigCache.getTypedConfig(appId, ThirdPlatformEnum.ALIPAY.getCode(), PaymentAlipayConfig.class);
            Config alipayConfig = buildAlipayConfig(config);
            Factory.setOptions(alipayConfig);

            AlipayTradeAppPayResponse response = Factory.Payment
                    .App()
                    .pay(subject, outTradeNo, totalAmount);

            if (ResponseChecker.success(response)) {
                result.setSuccess(true);
                result.setOrderInfo(response.getBody());
            } else {
                result.setSuccess(false);
                result.setErrorMessage("创建交易失败：响应为空或无 body");
            }
        } catch (Exception e) {
            log.error("创建支付宝交易异常", e);
            result.setSuccess(false);
            result.setErrorMessage("创建交易异常: " + e.getMessage());
        }
        return result;
    }


    /**
     * 支付宝回调处理
     */
    public PaymentAlipayClientResult alipayNotificationCallback(Map<String, String> params, Long appId) {
        PaymentAlipayClientResult result = new PaymentAlipayClientResult();
        try {
            PaymentAlipayConfig config = paymentAppConfigCache.getTypedConfig(appId, ThirdPlatformEnum.ALIPAY.getCode(), PaymentAlipayConfig.class);
            Config alipayConfig = buildAlipayConfig(config);
            Factory.setOptions(alipayConfig);

            boolean signVerified = Factory.Payment.Common().verifyNotify(params);
            if (!signVerified) {
                result.setSuccess(false);
                result.setErrorMessage("支付宝签名验证失败");
                return result;
            }

            String outTradeNo = params.get("out_trade_no");
            String tradeStatus = params.get("trade_status");

            log.info("收到支付宝支付通知，outTradeNo={}, tradeStatus={}, totalAmount={}", outTradeNo, tradeStatus, params.get("total_amount"));
            result.setSuccess(true);
            result.setNotifyParams(params);
            result.setTradeStatus(tradeStatus);
        } catch (Exception e) {
            log.error("支付宝回调处理失败", e);
            result.setSuccess(false);
            result.setErrorMessage("回调处理异常: " + e.getMessage());
        }
        return result;
    }
    private Config buildAlipayConfig(PaymentAlipayConfig config) {
        Config alipayConfig = new Config();
        alipayConfig.protocol = "https";
        alipayConfig.gatewayHost = "openapi.alipay.com";
        alipayConfig.signType = "RSA2";

        alipayConfig.appId = config.getAppId();
        alipayConfig.merchantPrivateKey = config.getPrivateKey();
        alipayConfig.alipayPublicKey = config.getAliPayPublicKey();
        alipayConfig.notifyUrl = config.getCallbackUrl();

        return alipayConfig;
    }
}

package com.mega.platform.cloud;

import com.mega.platform.cloud.core.utils.SpringUtils;
import lombok.Getter;

@Getter
public enum PaymentErrorCode {

    ERR_0(0),
    ;

    private final Integer code;

    PaymentErrorCode(Integer code) {
        this.code = code;
    }

    public static PaymentErrorCode getExchangeCode(Integer code) {
        for (PaymentErrorCode exchangeCode : PaymentErrorCode.values()) {
            if (exchangeCode.getCode().equals(code)) {
                return exchangeCode;
            }
        }
        return null;
    }

    public String getMessage() {
        return SpringUtils.getLocaleMessage(this.code);
    }
}

package com.mega.platform.cloud.payment.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.mega.platform.cloud.common.enums.OrderStatusEnum;
import com.mega.platform.cloud.common.enums.ThirdPlatformEnum;
import com.mega.platform.cloud.common.mapper.PaymentCallbackLogMapper;
import com.mega.platform.cloud.common.mapper.PaymentOrderDeviceInfoMapper;
import com.mega.platform.cloud.common.mapper.PaymentOrderMapper;
import com.mega.platform.cloud.common.mapper.PaymentProductConfigMapper;
import com.mega.platform.cloud.common.utils.EnvUtil;
import com.mega.platform.cloud.data.entity.PaymentCallbackLog;
import com.mega.platform.cloud.data.entity.PaymentOrder;
import com.mega.platform.cloud.data.entity.PaymentOrderDeviceInfo;
import com.mega.platform.cloud.data.entity.PaymentProductConfig;
import com.mega.platform.cloud.data.vo.payment.PaymentAlipayCallbackReqVO;
import com.mega.platform.cloud.data.vo.payment.PaymentAlipayCallbackRespVO;
import com.mega.platform.cloud.data.vo.payment.PaymentAlipayCreateReqVO;
import com.mega.platform.cloud.data.vo.payment.PaymentAlipayCreateRespVO;
import com.mega.platform.cloud.payment.client.PaymentAlipayClient;
import com.mega.platform.cloud.payment.entity.PaymentAlipayClientResult;
import com.mega.platform.cloud.payment.util.OrderNoUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

@Service
@RequiredArgsConstructor
@Slf4j
public class PaymentAlipayVerificationService {
    private final PaymentAlipayClient paymentAlipayClient;
    private final PaymentOrderMapper paymentOrderMapper;
    private final PaymentOrderDeviceInfoMapper paymentOrderDeviceInfoMapper;
    private final PaymentProductConfigMapper paymentProductConfigMapper;
    private final PaymentCallbackLogMapper paymentCallbackLogMapper;
    private final ObjectMapper objectMapper;

    /**
     * 创建支付宝交易（APP支付）
     */
    @Transactional(rollbackFor = Exception.class)
    public PaymentAlipayCreateRespVO createAlipayTransaction(PaymentAlipayCreateReqVO vo){
        // 商品配置
        PaymentProductConfig config = paymentProductConfigMapper.selectOne(
                new PaymentProductConfig()
                        .setExternalProductId(vo.getProductId())
                        .setThirdPlatformId(ThirdPlatformEnum.ALIPAY.getCode()));
        String outTradeNo = OrderNoUtil.generate(ThirdPlatformEnum.ALIPAY.getPlatformCode());

        PaymentAlipayClientResult clientResult = paymentAlipayClient.createAlipayTransaction(
                outTradeNo, config.getAmount().toPlainString(), config.getRemark(), vo.getAppId());

        PaymentAlipayCreateRespVO respVO = new PaymentAlipayCreateRespVO();
        PaymentOrder order = new PaymentOrder();
        PaymentOrderDeviceInfo deviceInfo = new PaymentOrderDeviceInfo();
        respVO.setOutTradeNo(outTradeNo);
        if (clientResult.isSuccess()) {
            order.setOrderNo(outTradeNo)
                    .setPaymentProductConfigId(config.getId())
                    .setTotalAmount(config.getAmount())
                    .setCurrency(config.getCurrency())
                    .setStatus((byte) OrderStatusEnum.INIT.getCode())
                    .setSandBox((short) (EnvUtil.isDev() ? 1 : 0))
                    .setDelsign((byte) 0)
                    .setCreateTime(new Date());

            respVO.setSuccess(true).setOrderInfo(clientResult.getOrderInfo());
        } else {
            order.setOrderNo(outTradeNo)
                    .setStatus((byte) OrderStatusEnum.FAIL.getCode())
                    .setDelsign((byte) 0);
            respVO.setSuccess(false).setErrorMessage(clientResult.getErrorMessage());
        }

        paymentOrderMapper.insertSelective(order);
        BeanUtils.copyProperties(vo, deviceInfo);
        deviceInfo.setPaymentOrderId(order.getId());
        paymentOrderDeviceInfoMapper.insertSelective(deviceInfo);

        return respVO;
    }

    /**
     * 支付宝回调处理
     */
    @Transactional(rollbackFor = Exception.class)
    public PaymentAlipayCallbackRespVO alipayCallback(PaymentAlipayCallbackReqVO vo) {
        PaymentAlipayCallbackRespVO respVO = new PaymentAlipayCallbackRespVO();
        PaymentCallbackLog logEntry = new PaymentCallbackLog();

        try {
            Map<String, String> params = vo.getParams();

            Long appId = vo.getAppId();

            PaymentAlipayClientResult result = paymentAlipayClient.alipayNotificationCallback(params, appId);

            logEntry.setProjectAppId(appId)
                    .setPlatformCode(ThirdPlatformEnum.ALIPAY.getPlatformCode())
                    .setRawContent(objectMapper.writeValueAsString(params))
                    .setProcessTime(new Date())
                    .setDelsign((byte) 0);

            if (result.isSuccess()) {
                String outTradeNo = params.get("out_trade_no");
                PaymentOrder order = paymentOrderMapper.selectOne(new PaymentOrder().setOrderNo(outTradeNo));
                logEntry.setPaymentOrderId(order.getId());
                logEntry.setOrderNo(outTradeNo)
                        .setParsedData(objectMapper.writeValueAsString(params))
                        .setStatus("SUCCESS")
                        .setNotifyTime(new Date());

                order.setStatus((byte) OrderStatusEnum.SUCCESS.getCode());
                order.setPayTime(new Date());
                paymentOrderMapper.updateByPrimaryKeySelective(order);
                PaymentProductConfig config = paymentProductConfigMapper.selectOne(
                        new PaymentProductConfig()
                                .setId(order.getPaymentProductConfigId()));
                respVO.setSuccess(true).setExternalProductId(config.getExternalProductId()).setOutTradeNo(outTradeNo).setTradeStatus(result.getTradeStatus());
            } else {
                logEntry.setStatus("FAILED").setErrorMessage(result.getErrorMessage());
                respVO.setSuccess(false).setErrorMessage(result.getErrorMessage());
            }
        } catch (Exception e) {
            logEntry.setStatus("FAILED").setErrorMessage(e.getMessage());
            respVO.setSuccess(false).setErrorMessage(e.getMessage());
        }

        paymentCallbackLogMapper.insertSelective(logEntry);
        return respVO;
    }

}

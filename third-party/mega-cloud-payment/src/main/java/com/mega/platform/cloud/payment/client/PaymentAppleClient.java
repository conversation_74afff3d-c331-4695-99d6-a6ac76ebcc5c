package com.mega.platform.cloud.payment.client;

import com.apple.itunes.storekit.client.APIException;
import com.apple.itunes.storekit.client.AppStoreServerAPIClient;
import com.apple.itunes.storekit.model.Environment;
import com.apple.itunes.storekit.model.JWSTransactionDecodedPayload;
import com.apple.itunes.storekit.model.ResponseBodyV2DecodedPayload;
import com.apple.itunes.storekit.verification.SignedDataVerifier;
import com.apple.itunes.storekit.verification.VerificationException;
import com.mega.platform.cloud.common.enums.ThirdPlatformEnum;
import com.mega.platform.cloud.common.utils.EnvUtil;
import com.mega.platform.cloud.data.entity.PaymentAppleConfig;
import com.mega.platform.cloud.payment.cache.PaymentAppConfigCache;
import com.mega.platform.cloud.payment.entity.PaymentAppleClientResult;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.util.Set;
@Component
@RequiredArgsConstructor
@Slf4j
public class PaymentAppleClient {
    private static final String[] ROOT_CA_PATHS = {
            "/cert/AppleComputerRootCertificate.cer",
            "/cert/AppleIncRootCertificate.cer",
            "/cert/AppleRootCA-G2.cer",
            "/cert/AppleRootCA-G3.cer"
    };

    private final PaymentAppConfigCache paymentAppConfigCache;

    public PaymentAppleClientResult verifyAppleTransaction(String originalTransactionId, Long appId) throws IOException {
        PaymentAppleConfig paymentAppleConfig = paymentAppConfigCache.getTypedConfig(appId, ThirdPlatformEnum.APPLE.getCode(), PaymentAppleConfig.class);
        Environment environment = Environment.SANDBOX;
        AppStoreServerAPIClient client = buildAppStoreClient(paymentAppleConfig.getPrivateKeyPath(), paymentAppleConfig.getKeyId(),
                paymentAppleConfig.getIssuerId(), paymentAppleConfig.getBundleId(), environment);
        SignedDataVerifier verifier = buildSignedDataVerifier(paymentAppleConfig.getBundleId(), paymentAppleConfig.getAppleAppId(), environment);
        PaymentAppleClientResult result = new PaymentAppleClientResult();
        String signedTransaction;
        try {
            signedTransaction = client.getTransactionInfo(originalTransactionId).getSignedTransactionInfo();
        } catch (IOException | RuntimeException | APIException e) {
            log.error("获取交易历史失败", e);
            result.setSuccess(false);
            result.setErrorMessage("交易信息获取失败，请稍后重试");
            return result;
        }

        JWSTransactionDecodedPayload payload;
        try {
            payload = verifier.verifyAndDecodeTransaction(signedTransaction);
        } catch (VerificationException e) {
            log.error("验证交易失败", e);
            result.setSuccess(false);
            result.setErrorMessage("交易验证失败");
            return result;
        }
        result.setSuccess(true);
        result.setPayload(payload);
        return result;
    }

    public PaymentAppleClientResult appleNotificationCallback(String signedPayload, Long appId) throws IOException, VerificationException {
        PaymentAppleClientResult result = new PaymentAppleClientResult();
        try {
            PaymentAppleConfig paymentAppleConfig = paymentAppConfigCache.getTypedConfig(appId, ThirdPlatformEnum.APPLE.getCode(), PaymentAppleConfig.class);
            Environment environment = Environment.SANDBOX;
            SignedDataVerifier verifier = buildSignedDataVerifier(paymentAppleConfig.getBundleId(), paymentAppleConfig.getAppleAppId(), environment);
            ResponseBodyV2DecodedPayload responsePayload = verifier.verifyAndDecodeNotification(signedPayload);
            JWSTransactionDecodedPayload jwsPayload = verifier.verifyAndDecodeTransaction(responsePayload.getData().getSignedTransactionInfo());

            log.info("收到 Apple Notification，appId={}, type={}", appId, responsePayload.getNotificationType());
            log.info("Notification 原始 Payload: {}", signedPayload);
            result.setPayload(jwsPayload);
            result.setNotificationType(responsePayload.getNotificationType().getValue());
            result.setSuccess(true);
            switch (responsePayload.getNotificationType()) {
                case ONE_TIME_CHARGE:
                    // 充值补充
                    break;
                case REFUND: {
                    // 退款
                    break;
                }
                case REFUND_REVERSED:
                    // 撤销退款
                    break;
                case TEST:
                    // 测试
                    break;
                default:
                    break;
            }
        } catch (Exception e) {
            result.setErrorMessage(e.getMessage());
            result.setSuccess(false);
        }
        return result;
    }


    private AppStoreServerAPIClient buildAppStoreClient(
            String privateKeyPath,
            String keyId,
            String issuerId,
            String bundleId,
            Environment environment) throws IOException {
        Resource resource = new ClassPathResource(privateKeyPath);
        String encodedKey = new String(resource.getInputStream().readAllBytes(), StandardCharsets.UTF_8);
        return new AppStoreServerAPIClient(encodedKey, keyId, issuerId, bundleId, environment);
    }


    private SignedDataVerifier buildSignedDataVerifier(
            String bundleId,
            Long appAppleId,
            Environment environment) throws IOException {
        Set<InputStream> rootCertStreams = loadRootCertificates();
        return new SignedDataVerifier(rootCertStreams, bundleId, appAppleId, environment, true);
    }

    private Set<InputStream> loadRootCertificates() throws IOException {
        Set<InputStream> certStreams = new java.util.HashSet<>();
        for (String path : ROOT_CA_PATHS) {
            Resource resource = new ClassPathResource(path);
            certStreams.add(resource.getInputStream());
        }
        return certStreams;
    }
}

package com.mega.platform.cloud.payment.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.mega.platform.cloud.common.enums.OrderStatusEnum;
import com.mega.platform.cloud.common.enums.ThirdPlatformEnum;
import com.mega.platform.cloud.common.mapper.PaymentCallbackLogMapper;
import com.mega.platform.cloud.common.mapper.PaymentOrderDeviceInfoMapper;
import com.mega.platform.cloud.common.mapper.PaymentOrderMapper;
import com.mega.platform.cloud.common.mapper.PaymentProductConfigMapper;
import com.mega.platform.cloud.common.utils.EnvUtil;
import com.mega.platform.cloud.data.entity.PaymentCallbackLog;
import com.mega.platform.cloud.data.entity.PaymentOrder;
import com.mega.platform.cloud.data.entity.PaymentOrderDeviceInfo;
import com.mega.platform.cloud.data.entity.PaymentProductConfig;
import com.mega.platform.cloud.data.vo.payment.PaymentWeChatCallbackReqVO;
import com.mega.platform.cloud.data.vo.payment.PaymentWeChatCallbackRespVO;
import com.mega.platform.cloud.data.vo.payment.PaymentWeChatCreateReqVO;
import com.mega.platform.cloud.data.vo.payment.PaymentWeChatCreateRespVO;
import com.mega.platform.cloud.payment.client.PaymentWeChatClient;
import com.mega.platform.cloud.payment.entity.PaymentWeChatClientResult;
import com.mega.platform.cloud.payment.util.OrderNoUtil;
import com.wechat.pay.java.service.partnerpayments.app.model.Transaction;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.Date;

@Slf4j
@RequiredArgsConstructor
public class PaymentWeChatVerificationService {
    private final PaymentWeChatClient paymentWeChatClient;
    private final PaymentOrderMapper paymentOrderMapper;
    private final PaymentOrderDeviceInfoMapper paymentOrderDeviceInfoMapper;
    private final PaymentProductConfigMapper paymentProductConfigMapper;
    private final PaymentCallbackLogMapper paymentCallbackLogMapper;
    private final ObjectMapper objectMapper;

    /**
     * 创建微信交易（APP支付）
     */
    @Transactional(rollbackFor = Exception.class)
    public PaymentWeChatCreateRespVO createWeChatTransaction(PaymentWeChatCreateReqVO vo) {
        PaymentWeChatCreateRespVO respVO = new PaymentWeChatCreateRespVO();

        // 查询商品配置
        PaymentProductConfig config = paymentProductConfigMapper.selectOne(
                new PaymentProductConfig()
                        .setExternalProductId(vo.getProductId())
                        .setThirdPlatformId(ThirdPlatformEnum.WECHAT.getCode()));

        String outTradeNo = OrderNoUtil.generate(ThirdPlatformEnum.WECHAT.getPlatformCode());

        PaymentWeChatClientResult clientResult = paymentWeChatClient.createWeChatTransaction(
                outTradeNo, config.getAmount().multiply(BigDecimal.valueOf(100)).intValue() + "", config.getRemark(), vo.getAppId());

        PaymentOrder order = new PaymentOrder();
        PaymentOrderDeviceInfo deviceInfo = new PaymentOrderDeviceInfo();
        respVO.setOutTradeNo(outTradeNo);

        if (clientResult.isSuccess()) {
            order.setOrderNo(outTradeNo)
                    .setPaymentProductConfigId(config.getId())
                    .setTotalAmount(config.getAmount())
                    .setCurrency(config.getCurrency())
                    .setStatus((byte) OrderStatusEnum.INIT.getCode())
                    .setSandBox((short) (EnvUtil.isDev() ? 1 : 0))
                    .setDelsign((byte) 0)
                    .setCreateTime(new Date());

            respVO.setSuccess(true)
                    .setPrepayId(clientResult.getPrepayId());

        } else {
            order.setOrderNo(outTradeNo)
                    .setStatus((byte) OrderStatusEnum.FAIL.getCode())
                    .setDelsign((byte) 0);

            respVO.setSuccess(false)
                    .setErrorMessage(clientResult.getErrorMessage());
        }

        paymentOrderMapper.insertSelective(order);
        BeanUtils.copyProperties(vo, deviceInfo);
        deviceInfo.setPaymentOrderId(order.getId());
        paymentOrderDeviceInfoMapper.insertSelective(deviceInfo);

        return respVO;
    }

    /**
     * 微信支付回调处理
     */
    @Transactional(rollbackFor = Exception.class)
    public PaymentWeChatCallbackRespVO wechatCallback(PaymentWeChatCallbackReqVO vo) {
        PaymentWeChatCallbackRespVO respVO = new PaymentWeChatCallbackRespVO();
        PaymentCallbackLog logEntry = new PaymentCallbackLog();

        try {
            Long appId = vo.getAppId();
            PaymentWeChatClientResult result = paymentWeChatClient.wechatNotificationCallback(
                    vo.getWechatpaySerial(), vo.getWechatpaySignature(), vo.getWechatpayTimestamp(),
                    vo.getWechatpayNonce(), vo.getBody(), appId);

            logEntry.setProjectAppId(appId)
                    .setPlatformCode(ThirdPlatformEnum.WECHAT.getPlatformCode())
                    .setRawContent(vo.getBody())
                    .setProcessTime(new Date())
                    .setDelsign((byte) 0);

            if (result.isSuccess()) {
                Transaction transaction = result.getTransaction();
                String outTradeNo = transaction.getOutTradeNo();

                PaymentOrder order = paymentOrderMapper.selectOne(new PaymentOrder().setOrderNo(outTradeNo));
                logEntry.setPaymentOrderId(order.getId());
                logEntry.setOrderNo(outTradeNo)
                        .setParsedData(objectMapper.writeValueAsString(transaction))
                        .setStatus("SUCCESS")
                        .setNotifyTime(new Date());

                order.setStatus((byte) OrderStatusEnum.SUCCESS.getCode());
                order.setPayTime(new Date());
                paymentOrderMapper.updateByPrimaryKeySelective(order);

                PaymentProductConfig config = paymentProductConfigMapper.selectOne(
                        new PaymentProductConfig().setId(order.getPaymentProductConfigId()));

                respVO.setSuccess(true)
                        .setExternalProductId(config.getExternalProductId())
                        .setOutTradeNo(outTradeNo);
            } else {
                logEntry.setStatus("FAILED").setErrorMessage(result.getErrorMessage());
                respVO.setSuccess(false).setErrorMessage(result.getErrorMessage());
            }
        } catch (Exception e) {
            logEntry.setStatus("FAILED").setErrorMessage(e.getMessage());
            respVO.setSuccess(false).setErrorMessage(e.getMessage());
        }

        paymentCallbackLogMapper.insertSelective(logEntry);
        return respVO;
    }
}

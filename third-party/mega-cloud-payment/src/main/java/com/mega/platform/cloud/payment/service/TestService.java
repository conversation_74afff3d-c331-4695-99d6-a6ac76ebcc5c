package com.mega.platform.cloud.payment.service;

import com.mega.platform.cloud.common.collection.Test;
import com.mega.platform.cloud.data.vo.TestReqVO;
import com.mega.platform.cloud.payment.dao.TestDao;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class TestService {

    private final TestDao testDao;
    private final MongoTemplate mongoTemplate;
    private final StringRedisTemplate stringRedisTemplate;
    private final KafkaTemplate<String, String> kafkaTemplate;

    @Autowired
    public TestService(TestDao testDao, MongoTemplate mongoTemplate, StringRedisTemplate stringRedisTemplate, KafkaTemplate<String, String> kafkaTemplate) {
        this.testDao = testDao;
        this.mongoTemplate = mongoTemplate;
        this.stringRedisTemplate = stringRedisTemplate;
        this.kafkaTemplate = kafkaTemplate;
    }

    public void mysqlTest(TestReqVO vo) {
        testDao.mysqlTest(vo.getName());
    }

    public void mongoTest(TestReqVO vo) {
        mongoTemplate.insert(new Test().setName(vo.getName()));
    }

    public void redisTest(TestReqVO vo) {
        System.out.println(stringRedisTemplate.opsForValue().get("test"));
    }

    public void kafkaTest(TestReqVO vo) {
        kafkaTemplate.send("test", vo.getName());
    }

    @KafkaListener(topics = "test", groupId = "test-group")
    public void kafkaTestListener(String message) {
        System.out.println("Received: " + message);
    }
}

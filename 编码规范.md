# Mega Cloud 编码规范

## 1. 命名规范

### 1.1 类命名约定
- **Controller**: XxxController
- **Service**: XxxService  
- **DAO**: XxxDAO/XxxDao
- **Entity**: XxxEntity
- **VO**: ModulePrefixXxxReqVO(入参)/ModulePrefixXxxRespVO(出参)
- **DTO**: XxxDTO
- **Config**: XxxConfig/XxxProperties
- **Exception**: XxxException
- **Enum**: XxxEnum/XxxCode
- **Utils**: XxxUtils

### 1.2 VO命名规范（严格遵守模块前缀）
- **mega-cloud-admin**: Admin前缀 (AdminProjectCreateReqVO, AdminProjectRespVO)
- **mega-cloud-auth**: Auth前缀 (AuthUserLoginReqVO, AuthTokenRespVO)
- **mega-cloud-payment**: Payment前缀 (PaymentOrderCreateReqVO, PaymentOrderRespVO)
- **mega-cloud-push**: Push前缀 (PushMessageSendReqVO, PushMessageRespVO)
- **mega-cloud-monitor**: Monitor前缀 (MonitorMetricsReqVO, MonitorMetricsRespVO)
- **mega-cloud-access**: Access前缀 (AccessPermissionReqVO, AccessPermissionRespVO)

### 1.3 方法命名约定
- **Controller**: list*/query*(查询), create*/add*/save*(创建), update*/modify*(更新), delete*/remove*(删除)
- **Service**: find*/get*/query*, create*/save*, update*/modify*, delete*/remove*
- **DAO**: select*/count*, insert*, update*, delete*

### 1.4 变量命名规范
- **ID字段**: entityId (userId, orderId)
- **时间字段**: createTime, updateTime
- **布尔字段**: isEnabled, hasPermission
- **状态字段**: status, delsign (0=正常, 1=删除)
- **常量**: UPPER_CASE_WITH_UNDERSCORES
- **VO中的delsign**: 必须是Integer类型

## 2. Controller开发规范

### 2.1 必要注解
```java
@RestController
@RequestMapping("/admin/api")
@Api(tags = "模块描述")
@Slf4j
public class XxxController {
    // 实现内容
}
```

### 2.2 依赖注入规范（强制要求）
- **必须**: 使用 `private final` 声明依赖
- **必须**: 构造函数注入 + `@Autowired`
- **禁止**: 字段注入

```java
private final XxxService xxxService;

@Autowired
public XxxController(XxxService xxxService) {
    this.xxxService = xxxService;
}
```

### 2.3 方法实现规范
- **方法注解**: `@PostMapping` + `@ApiOperation`
- **参数验证**: 使用 `@Validated` + `@RequestBody`
- **返回值**: 统一使用 `Result<T>` 包装
- **成功响应**: `Results.success(data)` 或 `Results.success()`
- **异常处理**: 通过抛出异常，由全局异常处理器统一处理

```java
@PostMapping("/public/auth/login")
@ApiOperation("管理员登录")
public Result<AdminAuthLoginRespVO> login(@Validated @RequestBody AdminAuthLoginReqVO reqVO) {
    AdminAuthLoginRespVO result = adminAuthService.login(reqVO);
    return Results.success(result);
}
```

### 2.4 上下文获取规范
```java
Long adminUserId = (Long) request.getAttribute(AdminAuthConstant.CONTEXT_ADMIN_USER_ID);
if (adminUserId == null) {
    throw new RuntimeException("用户未登录或登录已过期");
}
```

## 3. Service层开发规范

### 3.1 列表查询实现规范
- **PageResult与Result**: 位于mega-cloud-core包
- **Controller层**: 
  - 默认情况：list*/query*方法返回`Result<List<T>>`，负责包裹Result
  - 明确需要分页时：返回`Result<PageResult<T>>`
- **Service层**: 
  - 默认查询步骤：1.查询数据 2.转VO 3.直接返回`List<T>`，不包裹Result
  - 分页查询步骤：1.查总数 2.分页查数据 3.转VO 4.返回`PageResult<T>`，不包裹Result

### 3.2 Service层返回值规范
- **不包裹Result**: Service方法直接返回业务数据（如`PageResult<T>`、`T`、`List<T>`等）
- **异常处理**: 通过抛出异常来处理错误情况，由Controller层统一捕获并包裹为Result
- **Controller层职责**: 负责将Service返回的数据包裹为`Result<T>`格式，无返回值时使用`Result<?>`

### 3.3 Service注解规范
```java
@Service
@Slf4j
public class XxxService {
    private final XxxRepository xxxRepository;
    
    @Autowired
    public XxxService(XxxRepository xxxRepository) {
        this.xxxRepository = xxxRepository;
    }
}
```

## 4. VO开发规范

### 4.1 必要注解
```java
@Data
@Accessors(chain = true)
@ApiModel("项目创建请求参数")
public class AdminProjectCreateReqVO {
    @ApiModelProperty("项目名称")
    private String name;
    
    @ApiModelProperty("项目状态")
    private Integer status;
}
```

### 4.2 VO规范要求
- 使用 @Data + @Accessors(chain = true)
- 类级别添加 @ApiModel 注解
- 每个字段添加 @ApiModelProperty 注解，用注解描述替代字段注释
- 不需要实现 Serializable
- 驼峰命名
- 入参使用ReqVO后缀，出参使用RespVO后缀

## 5. Entity开发规范

### 5.1 必要注解
```java
@Data
@Entity
@Table(name = "table_name")
public class XxxEntity {
    // 字段定义
}
```

## 6. API设计规范

### 6.1 路由规范
- **URL格式**: /api/v1/module/resource
- **HTTP方法**: 所有请求统一使用POST方法
- **请求参数**: 所有请求参数封装在ReqVO类中，路由参数除外
- **响应参数**: 所有响应数据封装在RespVO类中
- **响应格式**: Results.success(data)

### 6.2 mega-cloud-admin路由规范
- `/admin/api/public/{controller}/xxx` - 无需鉴权接口（如登录接口）
- `/admin/api/system/{controller}/xxx` - 系统级别权限接口（如创建项目等）
- `/admin/api/{project_id}/{controller}/xxx` - 项目级别权限接口

## 7. 依赖注入规范（强制执行）

### 7.1 严格规则
- **必须**: 使用 `private final` 声明依赖
- **必须**: 构造函数注入 + `@Autowired`
- **禁止**: 字段注入 (`@Autowired` 在字段上)
- **禁止**: Setter注入 (可选依赖除外)

## 8. 其他开发规范

### 8.1 对象比较
- **禁止**: 使用 == 比较对象
- **必须**: 使用 equals() 方法

### 8.2 MyBatis规范
- 特殊字符使用 CDATA: `<![CDATA[ SQL ]]>`
- 参数化查询: #{param}
- SQL关键字: 大写
- 表/字段名: 小写下划线
- 动态SQL: `<if>`, `<choose>`, `<where>`, `<set>`

### 8.3 常量管理
- 按模块组织 (AdminConstant, CommonConstant)
- Redis键使用模块命名空间
- 使用 UPPER_CASE_WITH_UNDERSCORES

### 8.4 配置类规范
- 放在 config 包
- 使用 @ConfigurationProperties
- 命名: *Properties

### 8.5 Mapper接口规范
- 继承通用 Mapper
- 复杂查询使用 XML
- 使用 @Param 注解

---
type: "manual"
---

# Mega Cloud 项目开发规范

## 1. 项目架构

### 模块组织
- mega-cloud-core: 核心基础模块
- mega-cloud-common: 通用共享模块
- mega-cloud-data: 数据模块,提供公共的dto,entity,vo
- mega-cloud-generator: 代码生成模块
- mega-cloud-admin: 后台管理模块
- mega-cloud-access: 访问控制模块
- mega-cloud-monitor: 监控模块
- mega-cloud-micro-service: 微服务框架模块
- mega-cloud-auth: 认证授权模块
- mega-cloud-payment: 支付模块
- mega-cloud-push: 推送服务模块

## 2. 命名规范

### 类命名
- Controller: XxxController
- Service: XxxService
- DAO: XxxDAO/XxxDao
- Entity: XxxEntity
- VO: ModulePrefixXxxReqVO(入参)/ModulePrefixXxxRespVO(出参)
- DTO: XxxDTO
- Config: XxxConfig/XxxProperties
- Exception: XxxException
- Enum: XxxEnum/XxxCode
- Utils: XxxUtils

#### VO 命名规范
- **模组前缀**: 根据所属模组添加前缀
  - mega-cloud-admin: Admin前缀 (AdminProjectCreateReqVO, AdminProjectRespVO)
  - mega-cloud-auth: Auth前缀 (AuthUserLoginReqVO, AuthTokenRespVO)
  - mega-cloud-payment: Payment前缀 (PaymentOrderCreateReqVO, PaymentOrderRespVO)
  - mega-cloud-push: Push前缀 (PushMessageSendReqVO, PushMessageRespVO)
  - mega-cloud-monitor: Monitor前缀 (MonitorMetricsReqVO, MonitorMetricsRespVO)
  - mega-cloud-access: Access前缀 (AccessPermissionReqVO, AccessPermissionRespVO)

### 方法命名
- Controller: list*/query*(查询), create*/add*/save*(创建), update*/modify*(更新), delete*/remove*(删除)
- Service: find*/get*/query*, create*/save*, update*/modify*, delete*/remove*
- DAO: select*/count*, insert*, update*, delete*

### Controller/Service 约定
#### 列表查询实现规范
-  PageResult与Result位于mega-cloud-core包
- **Controller层**: 
  - 默认情况：list*/query*方法返回`Result<List<T>>`，负责包裹Result
  - 明确需要分页时：返回`Result<PageResult<T>>`
- **Service层**: 
  - 默认查询步骤：
    1. 查询数据
    2. 转VO
    3. 直接返回`List<T>`，不包裹Result
  - 分页查询步骤：
    1. 查总数
    2. 分页查数据
    3. 转VO
    4. 返回`PageResult<T>`，不包裹Result

#### Service层返回值规范
- **不包裹Result**: Service方法直接返回业务数据（如`PageResult<T>`、`T`、`List<T>`等）
- **异常处理**: 通过抛出异常来处理错误情况，由Controller层统一捕获并包裹为Result
- **Controller层职责**: 负责将Service返回的数据包裹为`Result<T>`格式，无返回值时使用`Result<?>`

### 变量命名
- ID字段: entityId (userId, orderId)
- 时间字段: createTime, updateTime
- 布尔字段: isEnabled, hasPermission
- 状态字段: status, delsign  (0=正常, 1=删除)
- 常量: UPPER_CASE_WITH_UNDERSCORES
- VO中使用的delsign一定是Integer类型

## 3. 代码规范

### Controller书写规范

#### 必要注解
```java
@RestController
@RequestMapping("/admin/api")
@Api(tags = "模块描述")
@Slf4j
public class XxxController {
    // 实现内容
}
```

#### 依赖注入规范
- **必须**: 使用 `private final` 声明依赖
- **必须**: 构造函数注入 + `@Autowired`
- **禁止**: 字段注入

```java
private final XxxService xxxService;

@Autowired
public XxxController(XxxService xxxService) {
    this.xxxService = xxxService;
}
```

#### 方法实现规范
- **方法注解**: `@PostMapping` + `@ApiOperation`
- **参数验证**: 使用 `@Validated` + `@RequestBody`
- **返回值**: 统一使用 `Result<T>` 包装
- **成功响应**: `Results.success(data)` 或 `Results.success()`
- **异常处理**: 通过抛出异常，由全局异常处理器统一处理

```java
@PostMapping("/public/auth/login")
@ApiOperation("管理员登录")
public Result<AdminAuthLoginRespVO> login(@Validated @RequestBody AdminAuthLoginReqVO reqVO) {
    AdminAuthLoginRespVO result = adminAuthService.login(reqVO);
    return Results.success(result);
}
```

#### 上下文获取规范
- 从 `HttpServletRequest` 获取用户信息
- 使用常量类定义上下文键名
- 进行空值检查并抛出合适异常

```java
Long adminUserId = (Long) request.getAttribute(AdminAuthConstant.CONTEXT_ADMIN_USER_ID);
if (adminUserId == null) {
    throw new RuntimeException("用户未登录或登录已过期");
}
```

### 其他必要注解
```java
// Service
@Service
@Slf4j

// Entity
@Data
@Entity
@Table(name = "table_name")

// VO
@Data
@Accessors(chain = true)
@ApiModel("描述")
// 字段使用 @ApiModelProperty(value = "描述", example = "示例值")
```

### API设计
- URL格式: /api/v1/module/resource
- HTTP方法: 所有请求统一使用POST方法
- 请求参数: 所有请求参数封装在ReqVO类中，路由参数除外
- 响应参数: 所有响应数据封装在RespVO类中
- 响应格式: Results.success(data)

#### mega-cloud-admin路由规范
- `/admin/api/public/{controller}/xxx` - 无需鉴权接口（如登录接口）
- `/admin/api/system/{controller}/xxx` - 系统级别权限接口（如创建项目等）
- `/admin/api/{project_id}/{controller}/xxx` - 项目级别权限接口

### 对象比较
- 禁止使用 == 比较对象
- 必须使用 equals() 方法

## 4. 依赖注入

### 构造函数注入(强制)
```java
@Service
@Slf4j
public class XxxService {
    private final XxxRepository xxxRepository;
    
    @Autowired
    public XxxService(XxxRepository xxxRepository) {
        this.xxxRepository = xxxRepository;
    }
}
```

### 规则
- **必须**: 使用 `private final` 声明依赖
- **必须**: 构造函数注入 + `@Autowired`
- **禁止**: 字段注入 (`@Autowired` 在字段上)
- **禁止**: Setter注入 (可选依赖除外)

## 5. 开发标准

### 配置类
- 放在 config 包
- 使用 @ConfigurationProperties
- 命名: *Properties

### 常量
- 按模块组织 (AdminConstant, CommonConstant)
- Redis键使用模块命名空间
- 使用 UPPER_CASE_WITH_UNDERSCORES

### VO规范
- 使用 @Data + @Accessors(chain = true)
- 类级别添加 @ApiModel 注解
- 每个字段添加 @ApiModelProperty 注解，用注解描述替代字段注释
- 不需要实现 Serializable
- 驼峰命名
- 入参使用ReqVO后缀，出参使用RespVO后缀

```java
@Data
@Accessors(chain = true)
@ApiModel("项目创建请求参数")
public class AdminProjectCreateReqVO {
    @ApiModelProperty("项目名称")
    private String name;
    
    @ApiModelProperty("项目状态")
    private Integer status;
}

@Data
@Accessors(chain = true)
@ApiModel("项目详情响应")
public class AdminProjectRespVO {
    @ApiModelProperty("项目ID")
    private Long id;
    
    @ApiModelProperty("项目名称")
    private String name;
}
```

### Mapper接口
- 继承通用 Mapper
- 复杂查询使用 XML
- 使用 @Param 注解

### MyBatis规范
- 特殊字符使用 CDATA: `<![CDATA[ SQL ]]>`
- 参数化查询: #{param}
- SQL关键字: 大写
- 表/字段名: 小写下划线
- 动态SQL: `<if>`, `<choose>`, `<where>`, `<set>`
